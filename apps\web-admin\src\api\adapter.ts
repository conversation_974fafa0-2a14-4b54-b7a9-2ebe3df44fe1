/**
 * API 接口适配器
 * 用于适配后台接口的数据格式和前端需求
 */

import type { PageFetchParams } from './request';

/**
 * 通用分页参数转换
 * 将前端分页参数转换为后台接口需要的格式
 */
export function transformPageParams(params: PageFetchParams) {
  const { pageNo = 1, pageSize = 10, ...rest } = params;
  
  // 根据您的后台接口调整参数名称
  return {
    page: pageNo,        // 或者使用 pageNum, current 等
    size: pageSize,      // 或者使用 limit, per_page 等
    ...rest,
  };
}

/**
 * 通用分页响应数据转换
 * 将后台返回的分页数据转换为前端需要的格式
 */
export function transformPageResponse(response: any) {
  // 根据您的后台接口调整字段名称
  return {
    items: response.data || response.records || response.list || [],
    total: response.total || response.totalCount || 0,
    page: response.page || response.current || 1,
    size: response.size || response.pageSize || 10,
  };
}

/**
 * 通用响应数据转换
 * 统一处理后台返回的数据格式
 */
export function transformResponse<T = any>(response: any): T {
  // 根据您的后台接口调整数据结构
  if (response.success || response.code === 0 || response.code === 200) {
    return response.data;
  }
  
  throw new Error(response.message || response.msg || '请求失败');
}

/**
 * 文件上传参数转换
 */
export function transformUploadParams(file: File, additionalParams?: Record<string, any>) {
  const formData = new FormData();
  formData.append('file', file);
  
  if (additionalParams) {
    Object.keys(additionalParams).forEach(key => {
      formData.append(key, additionalParams[key]);
    });
  }
  
  return formData;
}

/**
 * 日期范围参数转换
 */
export function transformDateRangeParams(dateRange: [string, string] | null, startField = 'startTime', endField = 'endTime') {
  if (!dateRange || dateRange.length !== 2) {
    return {};
  }
  
  return {
    [startField]: dateRange[0],
    [endField]: dateRange[1],
  };
}

/**
 * 状态值转换
 * 将前端的状态值转换为后台需要的格式
 */
export function transformStatus(status: string | number) {
  // 根据您的后台接口调整状态值映射
  const statusMap: Record<string, any> = {
    'active': 1,
    'inactive': 0,
    'enabled': 1,
    'disabled': 0,
    'pending': 0,
    'approved': 1,
    'rejected': 2,
  };
  
  return statusMap[status] ?? status;
}

/**
 * 排序参数转换
 */
export function transformSortParams(sorter: any) {
  if (!sorter || !sorter.field) {
    return {};
  }
  
  return {
    sortField: sorter.field,
    sortOrder: sorter.order === 'descend' ? 'desc' : 'asc',
  };
}
