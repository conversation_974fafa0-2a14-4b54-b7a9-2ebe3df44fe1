import { faker } from '@faker-js/faker';
import { verifyAccessToken } from '~/utils/jwt-utils';
import { unAuthorizedResponse, useResponseSuccess } from '~/utils/response';

const formatterCN = new Intl.DateTimeFormat('zh-CN', {
  timeZone: 'Asia/Shanghai',
  year: 'numeric',
  month: '2-digit',
  day: '2-digit',
  hour: '2-digit',
  minute: '2-digit',
  second: '2-digit',
});

export default eventHandler(async (event) => {
  const userinfo = verifyAccessToken(event);
  if (!userinfo) {
    return unAuthorizedResponse(event);
  }

  const id = getRouterParam(event, 'id');
  
  // 模拟获取用户详情
  const userDetail = {
    userId: id,
    username: faker.internet.username(),
    nickname: faker.person.fullName(),
    avatar: faker.image.avatar(),
    adminId: `admin${faker.string.numeric(3)}`,
    vipLevel: faker.number.int({ min: 0, max: 8 }),
    parentUserId: faker.datatype.boolean() ? `U${faker.string.numeric(8)}` : null,
    agencyRelation: faker.helpers.arrayElement(['direct', 'indirect', 'none']),
    languagePreference: faker.helpers.arrayElement(['zh-CN', 'en-US', 'zh-TW']),
    facebookId: faker.datatype.boolean() ? faker.string.numeric(15) : null,
    authCode: faker.string.alphanumeric(12).toUpperCase(),
    withdrawMethod: faker.helpers.arrayElement(['bank', 'alipay', 'wechat', 'paypal']),
    availableBalance: Number(faker.finance.amount({ min: 0, max: 50000, dec: 2 })),
    status: faker.helpers.arrayElement([0, 1]),
    userPerspective: faker.helpers.arrayElement(['customer', 'agent', 'admin']),
    createTime: formatterCN.format(
      faker.date.between({ from: '2022-01-01', to: '2025-01-01' }),
    ),
    updateTime: formatterCN.format(
      faker.date.between({ from: '2023-01-01', to: '2025-01-01' }),
    ),
  };

  // 模拟延迟
  await new Promise(resolve => setTimeout(resolve, 300));

  return useResponseSuccess(userDetail);
});
