# ADS 后台管理系统接口文档

## 文档概述

本文档详细描述了 ADS 后台管理系统的 API 接口规范，供前端开发人员与后端开发人员参考。文档根据系统各功能模块划分，包含了接口的请求方式、请求参数、响应结果等详细信息。

## 通用说明

### 接口格式

- 基础URL: `https://api.example.com/v1`
- 请求方法: 主要使用 GET、POST、PUT、DELETE 四种 HTTP 方法
- 请求头: 
  ```
  Content-Type: application/json
  Authorization: Bearer {token}
  ```

### 响应格式

所有接口返回JSON格式数据，基本结构如下:

```json
{
  "code": 0,       // 状态码，0表示成功，非0表示失败
  "message": "",   // 状态描述，成功时为空或"success"，失败时为错误信息
  "data": {}       // 返回的数据对象，失败时可为null
}
```

### 状态码说明

| 状态码 | 描述 |
| ------ | ---- |
| 0 | 成功 |
| 1001 | 参数错误 |
| 1002 | 权限不足 |
| 1003 | 资源不存在 |
| 1004 | 操作失败 |
| 1005 | 服务器内部错误 |
| 2001 | 用户未登录 |
| 2002 | 登录失败 |
| 2003 | 用户不存在 |
| 3001 | 数据库操作错误 |

## 目录

1. [认证相关接口](#1-认证相关接口)
2. [管理员管理接口](#2-管理员管理接口)
3. [产品库接口](#3-产品库接口)
4. [计划库接口](#4-计划库接口)
5. [优惠券接口](#5-优惠券接口)
6. [代运营接口](#6-代运营接口)
7. [授权管理接口](#7-授权管理接口)
8. [财务管理接口](#8-财务管理接口)
9. [用户计划管理接口](#9-用户计划管理接口)
10. [数据统计接口](#10-数据统计接口)
11. [用户管理接口](#11-用户管理接口)
12. [用户反馈接口](#12-用户反馈接口)
13. [系统设置接口](#13-系统设置接口)
14. [国家管理接口](#14-国家管理接口)

## 1. 认证相关接口

### 1.1 管理员登录

- **接口路径**: `/auth/login`
- **请求方式**: POST
- **接口描述**: 管理员账号登录系统

#### 请求参数

| 参数名   | 类型   | 必填 | 描述     |
| -------- | ------ | ---- | -------- |
| username | string | 是   | 用户名   |
| password | string | 是   | 登录密码 |

#### 返回结果

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "admin": {
      "id": 1,
      "username": "admin",
      "nickname": "管理员",
      "status": "active",
      "last_login_time": "2023-06-01 12:00:00"
    },
    "permissions": ["dashboard", "admin:view", "product:view", "..."]
  }
}
```

### 1.2 退出登录

- **接口路径**: `/auth/logout`
- **请求方式**: POST
- **接口描述**: 管理员退出系统

#### 请求参数

无

#### 返回结果

```json
{
  "code": 0,
  "message": "success",
  "data": null
}
```

### 1.3 获取当前管理员信息

- **接口路径**: `/auth/profile`
- **请求方式**: GET
- **接口描述**: 获取当前登录管理员的信息

#### 请求参数

无

#### 返回结果

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 1,
    "username": "admin",
    "nickname": "管理员",
    "status": "active",
    "last_login_time": "2023-06-01 12:00:00",
    "roles": [
      {
        "id": 1,
        "name": "超级管理员"
      }
    ],
    "permissions": ["dashboard", "admin:view", "product:view", "..."]
  }
}
```

### 1.4 修改密码

- **接口路径**: `/auth/change-password`
- **请求方式**: POST
- **接口描述**: 管理员修改自己的登录密码

#### 请求参数

| 参数名      | 类型   | 必填 | 描述     |
| ----------- | ------ | ---- | -------- |
| old_password | string | 是   | 原密码   |
| new_password | string | 是   | 新密码   |
| confirm_password | string | 是   | 确认新密码 |

#### 返回结果

```json
{
  "code": 0,
  "message": "密码修改成功",
  "data": null
}
```

## 2. 管理员管理接口

### 2.1 角色管理

#### 2.1.1 获取角色列表

- **接口路径**: `/admin/roles`
- **请求方式**: GET
- **接口描述**: 获取系统角色列表，支持分页和筛选

#### 请求参数

| 参数名    | 类型   | 必填 | 描述                  |
| --------- | ------ | ---- | --------------------- |
| page      | number | 否   | 页码，默认1           |
| page_size | number | 否   | 每页条数，默认20      |
| name      | string | 否   | 角色名称，支持模糊搜索 |
| status    | string | 否   | 角色状态(启用,禁用)   |

#### 返回结果

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "total": 100,
    "page": 1,
    "page_size": 20,
    "list": [
      {
        "id": 1,
        "name": "超级管理员",
        "description": "拥有所有权限",
        "status": "启用",
        "sort_order": 0,
        "create_time": "2023-01-01 00:00:00",
        "update_time": "2023-01-01 00:00:00"
      }
    ]
  }
}
```

#### 2.1.2 获取角色详情

- **接口路径**: `/admin/roles/{id}`
- **请求方式**: GET
- **接口描述**: 获取指定角色的详细信息

#### 请求参数

| 参数名 | 类型   | 必填 | 描述   |
| ------ | ------ | ---- | ------ |
| id     | number | 是   | 角色ID |

#### 返回结果

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 1,
    "name": "超级管理员",
    "description": "拥有所有权限",
    "parent_id": null,
    "status": "启用",
    "sort_order": 0,
    "create_time": "2023-01-01 00:00:00",
    "update_time": "2023-01-01 00:00:00",
    "permissions": [1, 2, 3, 4, 5]  // 权限ID列表
  }
}
```

#### 2.1.3 创建角色

- **接口路径**: `/admin/roles`
- **请求方式**: POST
- **接口描述**: 创建新的角色

#### 请求参数

| 参数名      | 类型     | 必填 | 描述                |
| ----------- | -------- | ---- | ------------------- |
| name        | string   | 是   | 角色名称            |
| description | string   | 否   | 角色描述            |
| parent_id   | number   | 否   | 父角色ID            |
| permissions | number[] | 是   | 权限ID列表          |
| status      | string   | 否   | 状态，默认为"启用"  |
| sort_order  | number   | 否   | 排序顺序，默认为0   |

#### 返回结果

```json
{
  "code": 0,
  "message": "创建成功",
  "data": {
    "id": 2,
    "name": "运营管理员",
    "description": "负责日常运营",
    "parent_id": 1,
    "status": "启用",
    "sort_order": 1,
    "create_time": "2023-06-01 12:00:00",
    "update_time": "2023-06-01 12:00:00"
  }
}
```

#### 2.1.4 更新角色

- **接口路径**: `/admin/roles/{id}`
- **请求方式**: PUT
- **接口描述**: 更新指定角色的信息

#### 请求参数

| 参数名      | 类型     | 必填 | 描述         |
| ----------- | -------- | ---- | ------------ |
| id          | number   | 是   | 角色ID       |
| name        | string   | 否   | 角色名称     |
| description | string   | 否   | 角色描述     |
| parent_id   | number   | 否   | 父角色ID     |
| permissions | number[] | 否   | 权限ID列表   |
| status      | string   | 否   | 状态         |
| sort_order  | number   | 否   | 排序顺序     |

#### 返回结果

```json
{
  "code": 0,
  "message": "更新成功",
  "data": {
    "id": 2,
    "name": "运营管理员",
    "description": "负责日常运营管理",
    "parent_id": 1,
    "status": "启用",
    "sort_order": 1,
    "update_time": "2023-06-01 13:00:00"
  }
}
```

#### 2.1.5 删除角色

- **接口路径**: `/admin/roles/{id}`
- **请求方式**: DELETE
- **接口描述**: 删除指定角色

#### 请求参数

| 参数名 | 类型   | 必填 | 描述   |
| ------ | ------ | ---- | ------ |
| id     | number | 是   | 角色ID |

#### 返回结果

```json
{
  "code": 0,
  "message": "删除成功",
  "data": null
}
```

#### 2.1.6 获取权限树

- **接口路径**: `/admin/permissions/tree`
- **请求方式**: GET
- **接口描述**: 获取系统权限树结构

#### 请求参数

| 参数名 | 类型   | 必填 | 描述               |
| ------ | ------ | ---- | ------------------ |
| type   | string | 否   | 权限类型(菜单,API) |

#### 返回结果

```json
{
  "code": 0,
  "message": "success",
  "data": [
    {
      "id": 1,
      "name": "仪表盘",
      "code": "dashboard",
      "type": "菜单",
      "path": "/dashboard",
      "children": []
    },
    {
      "id": 2,
      "name": "管理员管理",
      "code": "admin",
      "type": "菜单",
      "path": "/admin",
      "children": [
        {
          "id": 3,
          "name": "角色管理",
          "code": "admin:role",
          "type": "菜单",
          "path": "/admin/roles",
          "children": []
        },
        {
          "id": 4,
          "name": "管理员列表",
          "code": "admin:list",
          "type": "菜单",
          "path": "/admin/list",
          "children": []
        }
      ]
    }
  ]
}
```

### 2.2 管理员列表

#### 2.2.1 获取管理员列表

- **接口路径**: `/admin/list`
- **请求方式**: GET
- **接口描述**: 获取系统管理员列表，支持分页和筛选

#### 请求参数

| 参数名    | 类型   | 必填 | 描述                    |
| --------- | ------ | ---- | ----------------------- |
| page      | number | 否   | 页码，默认1             |
| page_size | number | 否   | 每页条数，默认20        |
| username  | string | 否   | 用户名，支持模糊搜索     |
| nickname  | string | 否   | 昵称，支持模糊搜索       |
| status    | string | 否   | 状态(active,inactive)   |

#### 返回结果

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "total": 50,
    "page": 1,
    "page_size": 20,
    "list": [
      {
        "id": 1,
        "username": "admin",
        "nickname": "超级管理员",
        "status": "active",
        "last_login_time": "2023-06-01 12:00:00",
        "create_time": "2023-01-01 00:00:00",
        "roles": [
          {
            "id": 1,
            "name": "超级管理员"
          }
        ]
      }
    ]
  }
}
```

#### 2.2.2 获取管理员详情

- **接口路径**: `/admin/list/{id}`
- **请求方式**: GET
- **接口描述**: 获取指定管理员的详细信息

#### 请求参数

| 参数名 | 类型   | 必填 | 描述       |
| ------ | ------ | ---- | ---------- |
| id     | number | 是   | 管理员ID   |

#### 返回结果

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 1,
    "username": "admin",
    "nickname": "超级管理员",
    "status": "active",
    "remark": "系统默认管理员",
    "last_login_time": "2023-06-01 12:00:00",
    "create_time": "2023-01-01 00:00:00",
    "update_time": "2023-01-01 00:00:00",
    "roles": [1]  // 角色ID列表
  }
}
```

#### 2.2.3 创建管理员

- **接口路径**: `/admin/list`
- **请求方式**: POST
- **接口描述**: 创建新的管理员账号

#### 请求参数

| 参数名   | 类型     | 必填 | 描述                  |
| -------- | -------- | ---- | --------------------- |
| username | string   | 是   | 用户名                |
| nickname | string   | 是   | 昵称                  |
| password | string   | 是   | 密码                  |
| roles    | number[] | 是   | 角色ID列表            |
| status   | string   | 否   | 状态，默认为"active"  |
| remark   | string   | 否   | 备注                  |

#### 返回结果

```json
{
  "code": 0,
  "message": "创建成功",
  "data": {
    "id": 2,
    "username": "operator",
    "nickname": "运营人员",
    "status": "active",
    "create_time": "2023-06-01 12:00:00"
  }
}
```

#### 2.2.4 更新管理员

- **接口路径**: `/admin/list/{id}`
- **请求方式**: PUT
- **接口描述**: 更新指定管理员的信息

#### 请求参数

| 参数名   | 类型     | 必填 | 描述       |
| -------- | -------- | ---- | ---------- |
| id       | number   | 是   | 管理员ID   |
| nickname | string   | 否   | 昵称       |
| roles    | number[] | 否   | 角色ID列表 |
| status   | string   | 否   | 状态       |
| remark   | string   | 否   | 备注       |

#### 返回结果

```json
{
  "code": 0,
  "message": "更新成功",
  "data": {
    "id": 2,
    "username": "operator",
    "nickname": "高级运营人员",
    "status": "active",
    "update_time": "2023-06-01 13:00:00"
  }
}
```

#### 2.2.5 重置管理员密码

- **接口路径**: `/admin/list/{id}/reset-password`
- **请求方式**: POST
- **接口描述**: 重置指定管理员的登录密码

#### 请求参数

| 参数名   | 类型   | 必填 | 描述       |
| -------- | ------ | ---- | ---------- |
| id       | number | 是   | 管理员ID   |
| password | string | 是   | 新密码     |

#### 返回结果

```json
{
  "code": 0,
  "message": "密码重置成功",
  "data": null
}
```

#### 2.2.6 删除管理员

- **接口路径**: `/admin/list/{id}`
- **请求方式**: DELETE
- **接口描述**: 删除指定管理员账号

#### 请求参数

| 参数名 | 类型   | 必填 | 描述     |
| ------ | ------ | ---- | -------- |
| id     | number | 是   | 管理员ID |

#### 返回结果

```json
{
  "code": 0,
  "message": "删除成功",
  "data": null
}
```

#### 2.2.7 管理员登录日志

- **接口路径**: `/admin/list/{id}/login-logs`
- **请求方式**: GET
- **接口描述**: 获取管理员的登录日志记录

#### 请求参数

| 参数名    | 类型   | 必填 | 描述             |
| --------- | ------ | ---- | ---------------- |
| id        | number | 是   | 管理员ID         |
| page      | number | 否   | 页码，默认1      |
| page_size | number | 否   | 每页条数，默认20 |
| start_time | string | 否   | 开始时间，格式：YYYY-MM-DD |
| end_time   | string | 否   | 结束时间，格式：YYYY-MM-DD |

#### 返回结果

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "total": 100,
    "page": 1,
    "page_size": 20,
    "list": [
      {
        "id": 1,
        "admin_id": 1,
        "ip": "***********",
        "user_agent": "Mozilla/5.0...",
        "login_time": "2023-06-01 12:00:00",
        "status": "成功",
        "remark": ""
      }
    ]
  }
}
``` 

## 3. 产品库接口

### 3.1 获取产品列表

- **接口路径**: `/products`
- **请求方式**: GET
- **接口描述**: 获取产品列表，支持分页和筛选

#### 请求参数

| 参数名    | 类型   | 必填 | 描述                 |
| --------- | ------ | ---- | -------------------- |
| page      | number | 否   | 页码，默认1          |
| page_size | number | 否   | 每页条数，默认20     |
| name      | string | 否   | 产品名称，支持模糊搜索 |
| category  | string | 否   | 产品分类             |
| company   | string | 否   | 公司名称，支持模糊搜索 |
| status    | string | 否   | 产品状态(生效,下架等) |

#### 返回结果

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "total": 100,
    "page": 1,
    "page_size": 20,
    "list": [
      {
        "id": 1,
        "product_code": "PROD_001",
        "name": "示例产品",
        "category": "游戏",
        "company": "示例公司",
        "status": "生效",
        "logo": "https://example.com/logo.png",
        "create_time": "2023-01-01 00:00:00"
      }
    ]
  }
}
```

### 3.2 获取产品详情

- **接口路径**: `/products/{id}`
- **请求方式**: GET
- **接口描述**: 获取指定产品的详细信息

#### 请求参数

| 参数名 | 类型   | 必填 | 描述   |
| ------ | ------ | ---- | ------ |
| id     | number | 是   | 产品ID |

#### 返回结果

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 1,
    "product_code": "PROD_001",
    "name": "示例产品",
    "category": "游戏",
    "company": "示例公司",
    "description": "这是一个示例产品的详细描述...",
    "status": "生效",
    "logo": "https://example.com/logo.png",
    "image": "https://example.com/image.png",
    "google_play_link": "https://play.google.com/store/apps/details?id=com.example",
    "app_store_link": "https://apps.apple.com/app/id123456789",
    "app_info": {
      "category": "Game",
      "languages": "English and 13 more",
      "age_rating": "9+",
      "ios_requires": "12+",
      "android_requires": "7.0 and up"
    },
    "create_time": "2023-01-01 00:00:00",
    "update_time": "2023-01-01 00:00:00",
    "images": [
      {
        "id": 1,
        "image_url": "https://example.com/screenshot1.png",
        "image_type": "截图",
        "sort_order": 0
      },
      {
        "id": 2,
        "image_url": "https://example.com/screenshot2.png",
        "image_type": "截图",
        "sort_order": 1
      }
    ]
  }
}
```

### 3.3 创建产品

- **接口路径**: `/products`
- **请求方式**: POST
- **接口描述**: 创建新的产品

#### 请求参数

| 参数名           | 类型     | 必填 | 描述               |
| ---------------- | -------- | ---- | ------------------ |
| name             | string   | 是   | 产品名称           |
| category         | string   | 是   | 产品分类           |
| company          | string   | 是   | 公司名称           |
| description      | string   | 是   | 产品简介           |
| logo             | string   | 是   | 产品Logo URL       |
| image            | string   | 是   | 产品主图URL        |
| google_play_link | string   | 否   | Google Play商店链接 |
| app_store_link   | string   | 否   | App Store商店链接   |
| app_info         | object   | 否   | 应用信息           |
| status           | string   | 否   | 状态，默认为"生效" |
| images           | array    | 否   | 产品图片列表       |

#### app_info参数说明

| 参数名           | 类型   | 必填 | 描述           |
| ---------------- | ------ | ---- | -------------- |
| category         | string | 否   | 应用分类       |
| languages        | string | 否   | 支持的语言     |
| age_rating       | string | 否   | 年龄评级       |
| ios_requires     | string | 否   | iOS系统要求    |
| android_requires | string | 否   | Android系统要求 |

#### images参数说明

| 参数名     | 类型   | 必填 | 描述               |
| ---------- | ------ | ---- | ------------------ |
| image_url  | string | 是   | 图片URL            |
| image_type | string | 是   | 图片类型(logo,截图等) |
| sort_order | number | 否   | 排序顺序，默认为0  |

#### 返回结果

```json
{
  "code": 0,
  "message": "创建成功",
  "data": {
    "id": 2,
    "product_code": "PROD_002",
    "name": "新产品",
    "create_time": "2023-06-01 12:00:00"
  }
}
```

### 3.4 更新产品

- **接口路径**: `/products/{id}`
- **请求方式**: PUT
- **接口描述**: 更新指定产品的信息

#### 请求参数

| 参数名           | 类型     | 必填 | 描述               |
| ---------------- | -------- | ---- | ------------------ |
| id               | number   | 是   | 产品ID             |
| name             | string   | 否   | 产品名称           |
| category         | string   | 否   | 产品分类           |
| company          | string   | 否   | 公司名称           |
| description      | string   | 否   | 产品简介           |
| logo             | string   | 否   | 产品Logo URL       |
| image            | string   | 否   | 产品主图URL        |
| google_play_link | string   | 否   | Google Play商店链接 |
| app_store_link   | string   | 否   | App Store商店链接   |
| app_info         | object   | 否   | 应用信息           |
| status           | string   | 否   | 状态               |

#### 返回结果

```json
{
  "code": 0,
  "message": "更新成功",
  "data": {
    "id": 1,
    "name": "更新后的产品名称",
    "update_time": "2023-06-01 13:00:00"
  }
}
```

### 3.5 删除产品

- **接口路径**: `/products/{id}`
- **请求方式**: DELETE
- **接口描述**: 删除指定产品

#### 请求参数

| 参数名 | 类型   | 必填 | 描述   |
| ------ | ------ | ---- | ------ |
| id     | number | 是   | 产品ID |

#### 返回结果

```json
{
  "code": 0,
  "message": "删除成功",
  "data": null
}
```

### 3.6 产品图片管理

#### 3.6.1 添加产品图片

- **接口路径**: `/products/{id}/images`
- **请求方式**: POST
- **接口描述**: 为指定产品添加图片

#### 请求参数

| 参数名     | 类型   | 必填 | 描述               |
| ---------- | ------ | ---- | ------------------ |
| id         | number | 是   | 产品ID             |
| image_url  | string | 是   | 图片URL            |
| image_type | string | 是   | 图片类型(logo,截图等) |
| sort_order | number | 否   | 排序顺序，默认为0  |

#### 返回结果

```json
{
  "code": 0,
  "message": "添加成功",
  "data": {
    "id": 3,
    "image_url": "https://example.com/screenshot3.png",
    "image_type": "截图",
    "sort_order": 2
  }
}
```

#### 3.6.2 删除产品图片

- **接口路径**: `/products/images/{id}`
- **请求方式**: DELETE
- **接口描述**: 删除指定产品图片

#### 请求参数

| 参数名 | 类型   | 必填 | 描述     |
| ------ | ------ | ---- | -------- |
| id     | number | 是   | 图片ID   |

#### 返回结果

```json
{
  "code": 0,
  "message": "删除成功",
  "data": null
}
```

## 4. 计划库接口

### 4.1 获取计划列表

- **接口路径**: `/plans`
- **请求方式**: GET
- **接口描述**: 获取计划列表，支持分页和筛选

#### 请求参数

| 参数名      | 类型   | 必填 | 描述                 |
| ----------- | ------ | ---- | -------------------- |
| page        | number | 否   | 页码，默认1          |
| page_size   | number | 否   | 每页条数，默认20     |
| name        | string | 否   | 计划名称，支持模糊搜索 |
| product_id  | number | 否   | 产品ID               |
| status      | string | 否   | 计划状态(激活,未激活等) |
| create_time | string | 否   | 创建时间，格式：YYYY-MM-DD |

#### 返回结果

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "total": 100,
    "page": 1,
    "page_size": 20,
    "list": [
      {
        "id": 1,
        "plan_code": "P_12345678",
        "name": "示例计划",
        "product": {
          "id": 1,
          "name": "示例产品",
          "logo": "https://example.com/logo.png"
        },
        "main_image": "https://example.com/plan_image.png",
        "status": "激活",
        "create_time": "2023-01-01 00:00:00"
      }
    ]
  }
}
```

### 4.2 获取计划详情

- **接口路径**: `/plans/{id}`
- **请求方式**: GET
- **接口描述**: 获取指定计划的详细信息

#### 请求参数

| 参数名 | 类型   | 必填 | 描述   |
| ------ | ------ | ---- | ------ |
| id     | number | 是   | 计划ID |

#### 返回结果

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 1,
    "plan_code": "P_12345678",
    "product_id": 1,
    "product": {
      "id": 1,
      "name": "示例产品",
      "logo": "https://example.com/logo.png"
    },
    "name": "示例计划",
    "description": "这是一个示例计划的详细描述...",
    "main_image": "https://example.com/plan_image.png",
    "status": "激活",
    "promotion_content": [
      {
        "title": "Promotional Link",
        "content": "https://example.com/product"
      },
      {
        "title": "Ad Display Format",
        "content": "Image"
      }
    ],
    "placement_rules": [
      {
        "title": "Ad Placement Strategy",
        "content": "CPM"
      },
      {
        "title": "Ad Placement Cost",
        "content": "Smart Optimization"
      }
    ],
    "audience_criteria": [
      {
        "title": "Location",
        "content": "Global"
      }
    ],
    "create_time": "2023-01-01 00:00:00",
    "update_time": "2023-01-01 00:00:00"
  }
}
```

### 4.3 创建计划

- **接口路径**: `/plans`
- **请求方式**: POST
- **接口描述**: 创建新的计划

#### 请求参数

| 参数名            | 类型   | 必填 | 描述                 |
| ----------------- | ------ | ---- | -------------------- |
| product_id        | number | 是   | 产品ID               |
| name              | string | 是   | 计划名称             |
| description       | string | 是   | 计划简介             |
| main_image        | string | 是   | 主图URL              |
| promotion_content | array  | 否   | 投放内容             |
| placement_rules   | array  | 否   | 投放规则             |
| audience_criteria | array  | 否   | 用户定向条件         |
| status            | string | 否   | 状态，默认为"激活"   |

#### promotion_content参数说明

| 参数名  | 类型   | 必填 | 描述     |
| ------- | ------ | ---- | -------- |
| title   | string | 是   | 标题     |
| content | string | 是   | 内容     |

#### placement_rules参数说明

| 参数名  | 类型   | 必填 | 描述     |
| ------- | ------ | ---- | -------- |
| title   | string | 是   | 标题     |
| content | string | 是   | 内容     |

#### audience_criteria参数说明

| 参数名  | 类型   | 必填 | 描述     |
| ------- | ------ | ---- | -------- |
| title   | string | 是   | 标题     |
| content | string | 是   | 内容     |

#### 返回结果

```json
{
  "code": 0,
  "message": "创建成功",
  "data": {
    "id": 2,
    "plan_code": "P_23456789",
    "name": "新计划",
    "create_time": "2023-06-01 12:00:00"
  }
}
```

### 4.4 更新计划

- **接口路径**: `/plans/{id}`
- **请求方式**: PUT
- **接口描述**: 更新指定计划的信息

#### 请求参数

| 参数名            | 类型   | 必填 | 描述           |
| ----------------- | ------ | ---- | -------------- |
| id                | number | 是   | 计划ID         |
| name              | string | 否   | 计划名称       |
| description       | string | 否   | 计划简介       |
| main_image        | string | 否   | 主图URL        |
| promotion_content | array  | 否   | 投放内容       |
| placement_rules   | array  | 否   | 投放规则       |
| audience_criteria | array  | 否   | 用户定向条件   |
| status            | string | 否   | 状态           |

#### 返回结果

```json
{
  "code": 0,
  "message": "更新成功",
  "data": {
    "id": 1,
    "name": "更新后的计划名称",
    "update_time": "2023-06-01 13:00:00"
  }
}
```

### 4.5 删除计划

- **接口路径**: `/plans/{id}`
- **请求方式**: DELETE
- **接口描述**: 删除指定计划

#### 请求参数

| 参数名 | 类型   | 必填 | 描述   |
| ------ | ------ | ---- | ------ |
| id     | number | 是   | 计划ID |

#### 返回结果

```json
{
  "code": 0,
  "message": "删除成功",
  "data": null
}
```

### 4.6 修改计划状态

- **接口路径**: `/plans/{id}/status`
- **请求方式**: PUT
- **接口描述**: 修改指定计划的状态

#### 请求参数

| 参数名 | 类型   | 必填 | 描述                    |
| ------ | ------ | ---- | ----------------------- |
| id     | number | 是   | 计划ID                  |
| status | string | 是   | 新状态(激活,未激活等)   |

#### 返回结果

```json
{
  "code": 0,
  "message": "状态更新成功",
  "data": {
    "id": 1,
    "status": "未激活",
    "update_time": "2023-06-01 13:00:00"
  }
}
``` 

## 5. 优惠券接口

### 5.1 优惠券列表

#### 5.1.1 获取优惠券列表

- **接口路径**: `/coupons`
- **请求方式**: GET
- **接口描述**: 获取优惠券列表，支持分页和筛选

#### 请求参数

| 参数名      | 类型   | 必填 | 描述                                             |
| ----------- | ------ | ---- | ------------------------------------------------ |
| page        | number | 否   | 页码，默认1                                      |
| page_size   | number | 否   | 每页条数，默认20                                 |
| name        | string | 否   | 优惠券名称，支持模糊搜索                         |
| code        | string | 否   | 优惠券代码，支持模糊搜索                         |
| type        | string | 否   | 类型(增值券,抵扣券,团队券,自定义,固定金额券)    |
| status      | string | 否   | 优惠券状态(启用,禁用)                           |
| is_new_user | number | 否   | 是否新人券，1-是，0-否                           |
| start_time  | string | 否   | 开始时间范围起点，格式：YYYY-MM-DD              |
| end_time    | string | 否   | 开始时间范围终点，格式：YYYY-MM-DD              |

#### 返回结果

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "total": 100,
    "page": 1,
    "page_size": 20,
    "list": [
      {
        "id": 1,
        "coupon_code": "COUPON_001",
        "name": "新用户优惠券",
        "code": "NEW2023",
        "type": "固定金额券",
        "discount_type": "固定金额",
        "value": 10.00,
        "min_spend": 100.00,
        "status": "启用",
        "is_new_user": 1,
        "total_quantity": 1000,
        "remain_quantity": 800,
        "start_time": "2023-01-01 00:00:00",
        "end_time": "2023-12-31 23:59:59",
        "create_time": "2022-12-25 00:00:00"
      }
    ]
  }
}
```

#### 5.1.2 获取优惠券详情

- **接口路径**: `/coupons/{id}`
- **请求方式**: GET
- **接口描述**: 获取指定优惠券的详细信息

#### 请求参数

| 参数名 | 类型   | 必填 | 描述     |
| ------ | ------ | ---- | -------- |
| id     | number | 是   | 优惠券ID |

#### 返回结果

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 1,
    "coupon_code": "COUPON_001",
    "name": "新用户优惠券",
    "code": "NEW2023",
    "type": "固定金额券",
    "discount_type": "固定金额",
    "value": 10.00,
    "min_spend": 100.00,
    "max_discount": null,
    "description": "新用户首次充值满100元可使用",
    "status": "启用",
    "is_new_user": 1,
    "limit_count": 1,
    "image_url": "https://example.com/coupon.png",
    "total_quantity": 1000,
    "remain_quantity": 800,
    "start_time": "2023-01-01 00:00:00",
    "end_time": "2023-12-31 23:59:59",
    "admin_id": 1,
    "admin_name": "系统管理员",
    "create_time": "2022-12-25 00:00:00",
    "update_time": "2022-12-25 00:00:00"
  }
}
```

#### 5.1.3 创建优惠券

- **接口路径**: `/coupons`
- **请求方式**: POST
- **接口描述**: 创建新的优惠券

#### 请求参数

| 参数名         | 类型   | 必填 | 描述                                          |
| -------------- | ------ | ---- | --------------------------------------------- |
| name           | string | 是   | 优惠券名称                                    |
| code           | string | 是   | 优惠券代码                                    |
| type           | string | 是   | 类型(增值券,抵扣券,团队券,自定义,固定金额券)  |
| discount_type  | string | 是   | 折扣类型(固定金额,百分比)                     |
| value          | number | 是   | 优惠券价值                                    |
| min_spend      | number | 是   | 最低消费金额                                  |
| max_discount   | number | 否   | 最高优惠金额（百分比优惠时）                  |
| description    | string | 否   | 优惠券描述                                    |
| status         | string | 否   | 状态，默认为"启用"                           |
| is_new_user    | number | 否   | 是否新人券，1-是，0-否，默认为0              |
| limit_count    | number | 否   | 每个用户限领数量，默认为1                    |
| image_url      | string | 否   | 优惠券图片URL                                |
| total_quantity | number | 是   | 总数量（0表示无限）                          |
| start_time     | string | 是   | 开始时间，格式：YYYY-MM-DD HH:MM:SS          |
| end_time       | string | 是   | 结束时间，格式：YYYY-MM-DD HH:MM:SS          |

#### 返回结果

```json
{
  "code": 0,
  "message": "创建成功",
  "data": {
    "id": 2,
    "coupon_code": "COUPON_002",
    "name": "充值优惠券",
    "code": "RECHARGE2023",
    "create_time": "2023-06-01 12:00:00"
  }
}
```

#### 5.1.4 更新优惠券

- **接口路径**: `/coupons/{id}`
- **请求方式**: PUT
- **接口描述**: 更新指定优惠券的信息

#### 请求参数

| 参数名         | 类型   | 必填 | 描述                                          |
| -------------- | ------ | ---- | --------------------------------------------- |
| id             | number | 是   | 优惠券ID                                      |
| name           | string | 否   | 优惠券名称                                    |
| description    | string | 否   | 优惠券描述                                    |
| status         | string | 否   | 状态                                          |
| is_new_user    | number | 否   | 是否新人券，1-是，0-否                        |
| limit_count    | number | 否   | 每个用户限领数量                              |
| image_url      | string | 否   | 优惠券图片URL                                |
| total_quantity | number | 否   | 总数量（0表示无限）                          |
| remain_quantity| number | 否   | 剩余数量                                      |
| start_time     | string | 否   | 开始时间，格式：YYYY-MM-DD HH:MM:SS          |
| end_time       | string | 否   | 结束时间，格式：YYYY-MM-DD HH:MM:SS          |

#### 返回结果

```json
{
  "code": 0,
  "message": "更新成功",
  "data": {
    "id": 1,
    "name": "新用户专享优惠券",
    "update_time": "2023-06-01 13:00:00"
  }
}
```

#### 5.1.5 删除优惠券

- **接口路径**: `/coupons/{id}`
- **请求方式**: DELETE
- **接口描述**: 删除指定优惠券

#### 请求参数

| 参数名 | 类型   | 必填 | 描述     |
| ------ | ------ | ---- | -------- |
| id     | number | 是   | 优惠券ID |

#### 返回结果

```json
{
  "code": 0,
  "message": "删除成功",
  "data": null
}
```

#### 5.1.6 修改优惠券状态

- **接口路径**: `/coupons/{id}/status`
- **请求方式**: PUT
- **接口描述**: 修改指定优惠券的状态

#### 请求参数

| 参数名 | 类型   | 必填 | 描述                 |
| ------ | ------ | ---- | -------------------- |
| id     | number | 是   | 优惠券ID             |
| status | string | 是   | 新状态(启用,禁用)    |

#### 返回结果

```json
{
  "code": 0,
  "message": "状态更新成功",
  "data": {
    "id": 1,
    "status": "禁用",
    "update_time": "2023-06-01 13:00:00"
  }
}
```

### 5.2 用户优惠券

#### 5.2.1 获取用户优惠券列表

- **接口路径**: `/user-coupons`
- **请求方式**: GET
- **接口描述**: 获取用户优惠券列表，支持分页和筛选

#### 请求参数

| 参数名      | 类型   | 必填 | 描述                          |
| ----------- | ------ | ---- | ----------------------------- |
| page        | number | 否   | 页码，默认1                   |
| page_size   | number | 否   | 每页条数，默认20              |
| user_id     | number | 否   | 用户ID                        |
| coupon_id   | number | 否   | 优惠券ID                      |
| status      | string | 否   | 状态(unused,used,expired)     |
| claim_time  | string | 否   | 领取时间，格式：YYYY-MM-DD    |
| expire_time | string | 否   | 过期时间，格式：YYYY-MM-DD    |

#### 返回结果

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "total": 100,
    "page": 1,
    "page_size": 20,
    "list": [
      {
        "id": 1,
        "user_id": 1001,
        "user_name": "用户1001",
        "coupon_id": 1,
        "coupon_name": "新用户优惠券",
        "coupon_code": "NEW2023",
        "type": "固定金额券",
        "value": 10.00,
        "status": "unused",
        "claim_time": "2023-01-15 10:00:00",
        "expired_time": "2023-12-31 23:59:59"
      }
    ]
  }
}
```

#### 5.2.2 获取用户优惠券详情

- **接口路径**: `/user-coupons/{id}`
- **请求方式**: GET
- **接口描述**: 获取指定用户优惠券的详细信息

#### 请求参数

| 参数名 | 类型   | 必填 | 描述         |
| ------ | ------ | ---- | ------------ |
| id     | number | 是   | 用户优惠券ID |

#### 返回结果

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 1,
    "user_id": 1001,
    "user_name": "用户1001",
    "coupon_id": 1,
    "coupon_name": "新用户优惠券",
    "coupon_code": "NEW2023",
    "type": "固定金额券",
    "discount_type": "固定金额",
    "value": 10.00,
    "min_spend": 100.00,
    "status": "unused",
    "used_time": null,
    "claim_time": "2023-01-15 10:00:00",
    "order_id": null,
    "discount_amount": null,
    "expired_time": "2023-12-31 23:59:59",
    "create_time": "2023-01-15 10:00:00",
    "update_time": "2023-01-15 10:00:00"
  }
}
```

#### 5.2.3 发放优惠券给用户

- **接口路径**: `/user-coupons/batch`
- **请求方式**: POST
- **接口描述**: 批量发放优惠券给指定用户

#### 请求参数

| 参数名    | 类型     | 必填 | 描述                |
| --------- | -------- | ---- | ------------------- |
| user_ids  | number[] | 是   | 用户ID列表          |
| coupon_id | number   | 是   | 优惠券ID            |
| remark    | string   | 否   | 备注                |

#### 返回结果

```json
{
  "code": 0,
  "message": "发放成功",
  "data": {
    "success_count": 3,
    "fail_count": 0,
    "success_users": [1001, 1002, 1003],
    "fail_users": []
  }
}
```

#### 5.2.4 更新用户优惠券状态

- **接口路径**: `/user-coupons/{id}/status`
- **请求方式**: PUT
- **接口描述**: 更新指定用户优惠券的状态

#### 请求参数

| 参数名 | 类型   | 必填 | 描述                         |
| ------ | ------ | ---- | ---------------------------- |
| id     | number | 是   | 用户优惠券ID                 |
| status | string | 是   | 新状态(unused,used,expired)  |

#### 返回结果

```json
{
  "code": 0,
  "message": "状态更新成功",
  "data": {
    "id": 1,
    "status": "used",
    "update_time": "2023-06-01 13:00:00"
  }
}
```

## 6. 代运营接口

### 6.1 获取代运营列表

- **接口路径**: `/agency-follows`
- **请求方式**: GET
- **接口描述**: 获取代运营跟随关系列表，支持分页和筛选

#### 请求参数

| 参数名      | 类型   | 必填 | 描述                          |
| ----------- | ------ | ---- | ----------------------------- |
| page        | number | 否   | 页码，默认1                   |
| page_size   | number | 否   | 每页条数，默认20              |
| user_id     | number | 否   | 用户ID                        |
| agency_id   | string | 否   | 代运营ID                      |
| status      | string | 否   | 状态(pending,active,ended)    |
| start_time  | string | 否   | 开始时间，格式：YYYY-MM-DD    |

#### 返回结果

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "total": 50,
    "page": 1,
    "page_size": 20,
    "list": [
      {
        "id": 1,
        "follow_code": "FOLLOW_001",
        "user_id": 1001,
        "user_name": "用户1001",
        "agency_id": "AGY12345678",
        "agency_name": "代运营商A",
        "status": "active",
        "start_time": "2023-01-15 10:00:00",
        "end_time": null,
        "create_time": "2023-01-15 10:00:00",
        "performance": {
          "roi": 2.5,
          "spend": 1000.00,
          "revenue": 2500.00
        }
      }
    ]
  }
}
```

### 6.2 获取代运营详情

- **接口路径**: `/agency-follows/{id}`
- **请求方式**: GET
- **接口描述**: 获取指定代运营跟随关系的详细信息

#### 请求参数

| 参数名 | 类型   | 必填 | 描述           |
| ------ | ------ | ---- | -------------- |
| id     | number | 是   | 代运营跟随ID   |

#### 返回结果

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 1,
    "follow_code": "FOLLOW_001",
    "user_id": 1001,
    "user_name": "用户1001",
    "agency_id": "AGY12345678",
    "agency_name": "代运营商A",
    "status": "active",
    "start_time": "2023-01-15 10:00:00",
    "end_time": null,
    "create_time": "2023-01-15 10:00:00",
    "update_time": "2023-01-15 10:00:00",
    "performance": {
      "roi": 2.5,
      "spend": 1000.00,
      "revenue": 2500.00
    }
  }
}
```

### 6.3 创建代运营跟随关系

- **接口路径**: `/agency-follows`
- **请求方式**: POST
- **接口描述**: 创建新的代运营跟随关系

#### 请求参数

| 参数名    | 类型   | 必填 | 描述                        |
| --------- | ------ | ---- | --------------------------- |
| user_id   | number | 是   | 用户ID                      |
| agency_id | string | 是   | 代运营ID                    |
| status    | string | 否   | 状态，默认为"pending"       |

#### 返回结果

```json
{
  "code": 0,
  "message": "创建成功",
  "data": {
    "id": 2,
    "follow_code": "FOLLOW_002",
    "user_id": 1002,
    "agency_id": "AGY12345678",
    "status": "pending",
    "create_time": "2023-06-01 12:00:00"
  }
}
```

### 6.4 更新代运营跟随状态

- **接口路径**: `/agency-follows/{id}/status`
- **请求方式**: PUT
- **接口描述**: 更新指定代运营跟随关系的状态

#### 请求参数

| 参数名   | 类型   | 必填 | 描述                         |
| -------- | ------ | ---- | ---------------------------- |
| id       | number | 是   | 代运营跟随ID                 |
| status   | string | 是   | 新状态(pending,active,ended) |
| end_time | string | 否   | 结束时间，格式：YYYY-MM-DD HH:MM:SS（当状态为ended时必填） |

#### 返回结果

```json
{
  "code": 0,
  "message": "状态更新成功",
  "data": {
    "id": 1,
    "status": "ended",
    "end_time": "2023-06-01 13:00:00",
    "update_time": "2023-06-01 13:00:00"
  }
}
```

### 6.5 获取代运营业绩统计

- **接口路径**: `/agency-follows/performance`
- **请求方式**: GET
- **接口描述**: 获取代运营业绩统计数据

#### 请求参数

| 参数名     | 类型   | 必填 | 描述                        |
| ---------- | ------ | ---- | --------------------------- |
| agency_id  | string | 否   | 代运营ID                    |
| user_id    | number | 否   | 用户ID                      |
| start_date | string | 否   | 开始日期，格式：YYYY-MM-DD  |
| end_date   | string | 否   | 结束日期，格式：YYYY-MM-DD  |

#### 返回结果

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "total": {
      "roi": 2.35,
      "spend": 5000.00,
      "revenue": 11750.00,
      "follow_count": 5
    },
    "monthly": [
      {
        "month": "2023-01",
        "roi": 2.1,
        "spend": 1000.00,
        "revenue": 2100.00
      },
      {
        "month": "2023-02",
        "roi": 2.4,
        "spend": 1500.00,
        "revenue": 3600.00
      },
      {
        "month": "2023-03",
        "roi": 2.5,
        "spend": 2500.00,
        "revenue": 6250.00
      }
    ]
  }
}
``` 

## 7. 授权管理接口

### 7.1 获取授权码列表

- **接口路径**: `/auth-codes`
- **请求方式**: GET
- **接口描述**: 获取授权码列表，支持分页和筛选

#### 请求参数

| 参数名      | 类型   | 必填 | 描述                       |
| ----------- | ------ | ---- | -------------------------- |
| page        | number | 否   | 页码，默认1                |
| page_size   | number | 否   | 每页条数，默认20           |
| auth_code   | string | 否   | 授权码，支持模糊搜索        |
| admin_id    | number | 否   | 所属管理员ID               |
| status      | string | 否   | 状态(未使用,已使用)        |
| user_id     | number | 否   | 使用该授权码的用户ID       |
| create_time | string | 否   | 创建时间，格式：YYYY-MM-DD |

#### 返回结果

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "total": 100,
    "page": 1,
    "page_size": 20,
    "list": [
      {
        "id": 1,
        "auth_code": "AC123456789",
        "admin_id": 1,
        "admin_name": "系统管理员",
        "status": "未使用",
        "user_id": null,
        "user_name": null,
        "used_time": null,
        "create_time": "2023-01-01 00:00:00"
      },
      {
        "id": 2,
        "auth_code": "AC234567890",
        "admin_id": 1,
        "admin_name": "系统管理员",
        "status": "已使用",
        "user_id": 1001,
        "user_name": "用户1001",
        "used_time": "2023-01-15 10:00:00",
        "create_time": "2023-01-01 00:00:00"
      }
    ]
  }
}
```

### 7.2 获取授权码详情

- **接口路径**: `/auth-codes/{id}`
- **请求方式**: GET
- **接口描述**: 获取指定授权码的详细信息

#### 请求参数

| 参数名 | 类型   | 必填 | 描述     |
| ------ | ------ | ---- | -------- |
| id     | number | 是   | 授权码ID |

#### 返回结果

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 1,
    "auth_code": "AC123456789",
    "admin_id": 1,
    "admin_name": "系统管理员",
    "status": "未使用",
    "user_id": null,
    "user_name": null,
    "used_time": null,
    "create_time": "2023-01-01 00:00:00",
    "update_time": "2023-01-01 00:00:00",
    "logs": [
      {
        "id": 1,
        "action": "创建",
        "admin_id": 1,
        "admin_name": "系统管理员",
        "user_id": null,
        "user_name": null,
        "ip": "***********",
        "remark": "管理员创建授权码",
        "create_time": "2023-01-01 00:00:00"
      }
    ]
  }
}
```

### 7.3 生成授权码

- **接口路径**: `/auth-codes/generate`
- **请求方式**: POST
- **接口描述**: 生成新的授权码

#### 请求参数

| 参数名  | 类型   | 必填 | 描述         |
| ------- | ------ | ---- | ------------ |
| count   | number | 否   | 生成数量，默认1 |
| remark  | string | 否   | 备注         |

#### 返回结果

```json
{
  "code": 0,
  "message": "生成成功",
  "data": {
    "auth_codes": [
      {
        "id": 3,
        "auth_code": "AC345678901",
        "admin_id": 1,
        "status": "未使用",
        "create_time": "2023-06-01 12:00:00"
      }
    ]
  }
}
```

### 7.4 废弃授权码

- **接口路径**: `/auth-codes/{id}/invalidate`
- **请求方式**: PUT
- **接口描述**: 废弃指定授权码

#### 请求参数

| 参数名 | 类型   | 必填 | 描述     |
| ------ | ------ | ---- | -------- |
| id     | number | 是   | 授权码ID |
| remark | string | 否   | 备注     |

#### 返回结果

```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "id": 1,
    "status": "已废弃",
    "update_time": "2023-06-01 13:00:00"
  }
}
```

### 7.5 获取授权操作日志

- **接口路径**: `/auth-codes/logs`
- **请求方式**: GET
- **接口描述**: 获取授权码操作日志列表，支持分页和筛选

#### 请求参数

| 参数名       | 类型   | 必填 | 描述                       |
| ------------ | ------ | ---- | -------------------------- |
| page         | number | 否   | 页码，默认1                |
| page_size    | number | 否   | 每页条数，默认20           |
| auth_code_id | number | 否   | 授权码ID                   |
| action       | string | 否   | 操作类型(创建,使用,废弃)   |
| admin_id     | number | 否   | 操作管理员ID               |
| user_id      | number | 否   | 操作用户ID                 |
| start_time   | string | 否   | 开始时间，格式：YYYY-MM-DD |
| end_time     | string | 否   | 结束时间，格式：YYYY-MM-DD |

#### 返回结果

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "total": 100,
    "page": 1,
    "page_size": 20,
    "list": [
      {
        "id": 1,
        "auth_code_id": 1,
        "auth_code": "AC123456789",
        "action": "创建",
        "admin_id": 1,
        "admin_name": "系统管理员",
        "user_id": null,
        "user_name": null,
        "ip": "***********",
        "remark": "管理员创建授权码",
        "create_time": "2023-01-01 00:00:00"
      },
      {
        "id": 2,
        "auth_code_id": 2,
        "auth_code": "AC234567890",
        "action": "使用",
        "admin_id": null,
        "admin_name": null,
        "user_id": 1001,
        "user_name": "用户1001",
        "ip": "***********",
        "remark": "用户使用授权码",
        "create_time": "2023-01-15 10:00:00"
      }
    ]
  }
}
```

## 8. 财务管理接口

### 8.1 充值管理

#### 8.1.1 获取充值记录列表

- **接口路径**: `/finance/recharge`
- **请求方式**: GET
- **接口描述**: 获取用户充值记录列表，支持分页和筛选

#### 请求参数

| 参数名      | 类型   | 必填 | 描述                                      |
| ----------- | ------ | ---- | ----------------------------------------- |
| page        | number | 否   | 页码，默认1                               |
| page_size   | number | 否   | 每页条数，默认20                          |
| user_id     | number | 否   | 用户ID                                    |
| type        | string | 否   | 充值类型                                  |
| amount_min  | number | 否   | 最小金额                                  |
| amount_max  | number | 否   | 最大金额                                  |
| status      | string | 否   | 状态(completed,pending,failed)           |
| transaction_code | string | 否   | 交易编码，支持模糊搜索                 |
| start_time  | string | 否   | 开始时间，格式：YYYY-MM-DD               |
| end_time    | string | 否   | 结束时间，格式：YYYY-MM-DD               |

#### 返回结果

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "total": 100,
    "page": 1,
    "page_size": 20,
    "list": [
      {
        "id": 1,
        "user_id": 1001,
        "user_name": "用户1001",
        "type": "余额充值",
        "amount": 100.00,
        "balance_before": 0.00,
        "balance_after": 100.00,
        "transaction_code": "T20230101001",
        "remark": "首次充值",
        "status": "completed",
        "create_time": "2023-01-01 10:00:00",
        "admin_id": 1,
        "admin_name": "系统管理员"
      }
    ]
  }
}
```

#### 8.1.2 为用户充值

- **接口路径**: `/finance/recharge`
- **请求方式**: POST
- **接口描述**: 为指定用户充值

#### 请求参数

| 参数名   | 类型   | 必填 | 描述                 |
| -------- | ------ | ---- | -------------------- |
| user_id  | number | 是   | 用户ID               |
| amount   | number | 是   | 充值金额             |
| type     | string | 是   | 充值类型             |
| remark   | string | 否   | 备注                 |

#### 返回结果

```json
{
  "code": 0,
  "message": "充值成功",
  "data": {
    "id": 2,
    "user_id": 1001,
    "amount": 200.00,
    "balance_before": 100.00,
    "balance_after": 300.00,
    "transaction_code": "T20230601001",
    "create_time": "2023-06-01 12:00:00"
  }
}
```

#### 8.1.3 批量为用户充值

- **接口路径**: `/finance/recharge/batch`
- **请求方式**: POST
- **接口描述**: 批量为多个用户充值

#### 请求参数

| 参数名   | 类型     | 必填 | 描述                 |
| -------- | -------- | ---- | -------------------- |
| user_ids | number[] | 是   | 用户ID列表           |
| amount   | number   | 是   | 充值金额             |
| type     | string   | 是   | 充值类型             |
| remark   | string   | 否   | 备注                 |

#### 返回结果

```json
{
  "code": 0,
  "message": "批量充值成功",
  "data": {
    "success_count": 3,
    "fail_count": 0,
    "success_users": [1001, 1002, 1003],
    "fail_users": []
  }
}
```

### 8.2 提现管理

#### 8.2.1 获取提现记录列表

- **接口路径**: `/finance/withdraw`
- **请求方式**: GET
- **接口描述**: 获取用户提现记录列表，支持分页和筛选

#### 请求参数

| 参数名      | 类型   | 必填 | 描述                                          |
| ----------- | ------ | ---- | --------------------------------------------- |
| page        | number | 否   | 页码，默认1                                   |
| page_size   | number | 否   | 每页条数，默认20                              |
| user_id     | number | 否   | 用户ID                                        |
| amount_min  | number | 否   | 最小金额                                      |
| amount_max  | number | 否   | 最大金额                                      |
| status      | string | 否   | 状态(pending,processing,completed,failed)    |
| withdrawal_code | string | 否   | 提现编码，支持模糊搜索                     |
| start_time  | string | 否   | 请求时间范围起点，格式：YYYY-MM-DD           |
| end_time    | string | 否   | 请求时间范围终点，格式：YYYY-MM-DD           |

#### 返回结果

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "total": 50,
    "page": 1,
    "page_size": 20,
    "list": [
      {
        "id": 1,
        "withdrawal_code": "W2023041901",
        "user_id": 1001,
        "user_name": "用户1001",
        "amount": 50.00,
        "status": "completed",
        "transaction_id": 10,
        "remark": "提现至银行卡",
        "request_time": "2023-04-19 10:00:00",
        "complete_time": "2023-04-20 10:00:00",
        "payment_method": {
          "id": 1,
          "type": "bank_card",
          "name": "招商银行",
          "masked_no": "6225****8888"
        }
      }
    ]
  }
}
```

#### 8.2.2 获取提现详情

- **接口路径**: `/finance/withdraw/{id}`
- **请求方式**: GET
- **接口描述**: 获取指定提现申请的详细信息

#### 请求参数

| 参数名 | 类型   | 必填 | 描述     |
| ------ | ------ | ---- | -------- |
| id     | number | 是   | 提现ID   |

#### 返回结果

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 1,
    "withdrawal_code": "W2023041901",
    "user_id": 1001,
    "user_name": "用户1001",
    "amount": 50.00,
    "status": "completed",
    "transaction_id": 10,
    "remark": "提现至银行卡",
    "request_time": "2023-04-19 10:00:00",
    "complete_time": "2023-04-20 10:00:00",
    "create_time": "2023-04-19 10:00:00",
    "update_time": "2023-04-20 10:00:00",
    "payment_method": {
      "id": 1,
      "payment_code": "PAY_001",
      "type": "bank_card",
      "name": "招商银行",
      "account_name": "张三",
      "masked_no": "6225****8888",
      "branch": "深圳分行",
      "currency": "USD"
    }
  }
}
```

#### 8.2.3 审核提现申请

- **接口路径**: `/finance/withdraw/{id}/audit`
- **请求方式**: PUT
- **接口描述**: 审核指定提现申请

#### 请求参数

| 参数名 | 类型   | 必填 | 描述                                       |
| ------ | ------ | ---- | ------------------------------------------ |
| id     | number | 是   | 提现ID                                     |
| status | string | 是   | 新状态(processing,completed,failed)       |
| remark | string | 否   | 备注                                       |

#### 返回结果

```json
{
  "code": 0,
  "message": "审核成功",
  "data": {
    "id": 2,
    "status": "processing",
    "update_time": "2023-06-01 13:00:00"
  }
}
```

### 8.3 交易记录

#### 8.3.1 获取交易记录列表

- **接口路径**: `/finance/transactions`
- **请求方式**: GET
- **接口描述**: 获取用户交易记录列表，支持分页和筛选

#### 请求参数

| 参数名      | 类型   | 必填 | 描述                                      |
| ----------- | ------ | ---- | ----------------------------------------- |
| page        | number | 否   | 页码，默认1                               |
| page_size   | number | 否   | 每页条数，默认20                          |
| user_id     | number | 否   | 用户ID                                    |
| type        | string | 否   | 交易类型                                  |
| amount_min  | number | 否   | 最小金额                                  |
| amount_max  | number | 否   | 最大金额                                  |
| status      | string | 否   | 状态(pending,completed,failed)           |
| transaction_code | string | 否   | 交易编码，支持模糊搜索                 |
| related_id  | string | 否   | 关联ID（如订单ID）                       |
| start_time  | string | 否   | 创建时间范围起点，格式：YYYY-MM-DD       |
| end_time    | string | 否   | 创建时间范围终点，格式：YYYY-MM-DD       |

#### 返回结果

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "total": 200,
    "page": 1,
    "page_size": 20,
    "list": [
      {
        "id": 1,
        "user_id": 1001,
        "user_name": "用户1001",
        "type": "余额充值",
        "amount": 100.00,
        "balance_before": 0.00,
        "balance_after": 100.00,
        "transaction_code": "T20230101001",
        "remark": "首次充值",
        "related_id": null,
        "status": "completed",
        "create_time": "2023-01-01 10:00:00"
      },
      {
        "id": 10,
        "user_id": 1001,
        "user_name": "用户1001",
        "type": "提现",
        "amount": -50.00,
        "balance_before": 100.00,
        "balance_after": 50.00,
        "transaction_code": "T20230419001",
        "remark": "提现至银行卡",
        "related_id": "W2023041901",
        "status": "completed",
        "create_time": "2023-04-19 10:00:00"
      }
    ]
  }
}
```

#### 8.3.2 获取交易详情

- **接口路径**: `/finance/transactions/{id}`
- **请求方式**: GET
- **接口描述**: 获取指定交易记录的详细信息

#### 请求参数

| 参数名 | 类型   | 必填 | 描述     |
| ------ | ------ | ---- | -------- |
| id     | number | 是   | 交易ID   |

#### 返回结果

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 1,
    "user_id": 1001,
    "user_name": "用户1001",
    "type": "余额充值",
    "amount": 100.00,
    "balance_before": 0.00,
    "balance_after": 100.00,
    "transaction_code": "T20230101001",
    "remark": "首次充值",
    "related_id": null,
    "status": "completed",
    "create_time": "2023-01-01 10:00:00",
    "update_time": "2023-01-01 10:00:00",
    "operator": {
      "id": 1,
      "name": "系统管理员",
      "type": "admin"
    }
  }
}
```

#### 8.3.3 导出交易记录

- **接口路径**: `/finance/transactions/export`
- **请求方式**: GET
- **接口描述**: 导出交易记录

#### 请求参数

| 参数名      | 类型   | 必填 | 描述                                      |
| ----------- | ------ | ---- | ----------------------------------------- |
| user_id     | number | 否   | 用户ID                                    |
| type        | string | 否   | 交易类型                                  |
| status      | string | 否   | 状态(pending,completed,failed)           |
| start_time  | string | 否   | 创建时间范围起点，格式：YYYY-MM-DD       |
| end_time    | string | 否   | 创建时间范围终点，格式：YYYY-MM-DD       |
| format      | string | 否   | 导出格式(csv,excel)，默认为excel         |

#### 返回结果

文件流，直接下载 

## 9. 用户计划管理接口

### 9.1 获取用户计划列表

- **接口路径**: `/user-plans`
- **请求方式**: GET
- **接口描述**: 获取用户计划列表，支持分页和筛选

#### 请求参数

| 参数名          | 类型   | 必填 | 描述                            |
| --------------- | ------ | ---- | ------------------------------- |
| page            | number | 否   | 页码，默认1                     |
| page_size       | number | 否   | 每页条数，默认20                |
| user_id         | number | 否   | 用户ID                          |
| plan_id         | number | 否   | 计划ID                          |
| placement_status| string | 否   | 投放状态(投放中,暂停等)         |
| vip_level       | number | 否   | VIP等级                         |
| min_amount      | number | 否   | 最低投放金额                    |
| max_amount      | number | 否   | 最高投放金额                    |
| is_official     | number | 否   | 是否官方推荐，1-是，0-否        |
| create_time     | string | 否   | 创建时间，格式：YYYY-MM-DD      |

#### 返回结果

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "total": 100,
    "page": 1,
    "page_size": 20,
    "list": [
      {
        "id": 1,
        "plan_id": "P_12345678",
        "plan_name": "示例计划",
        "user_id": 1001,
        "user_name": "用户1001",
        "click_price": 0.5000,
        "vip_level": 2,
        "impression_count": 1000,
        "click_count": 50,
        "min_amount": 100.00,
        "max_amount": 1000.00,
        "invested_amount": 500.00,
        "consumed_amount": 25.00,
        "placement_status": "投放中",
        "is_official": 1,
        "create_time": "2023-01-15 10:00:00"
      }
    ]
  }
}
```

### 9.2 获取用户计划详情

- **接口路径**: `/user-plans/{id}`
- **请求方式**: GET
- **接口描述**: 获取指定用户计划的详细信息

#### 请求参数

| 参数名 | 类型   | 必填 | 描述       |
| ------ | ------ | ---- | ---------- |
| id     | number | 是   | 用户计划ID |

#### 返回结果

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 1,
    "plan_id": "P_12345678",
    "plan": {
      "id": 1,
      "plan_code": "P_12345678",
      "name": "示例计划",
      "product": {
        "id": 1,
        "name": "示例产品",
        "logo": "https://example.com/logo.png"
      },
      "main_image": "https://example.com/plan_image.png"
    },
    "user_id": 1001,
    "user_name": "用户1001",
    "click_price": 0.5000,
    "vip_level": 2,
    "impression_count": 1000,
    "click_count": 50,
    "min_amount": 100.00,
    "max_amount": 1000.00,
    "invested_amount": 500.00,
    "consumed_amount": 25.00,
    "placement_status": "投放中",
    "consumption_minutes": 1,
    "failure_notes": null,
    "is_official": 1,
    "create_time": "2023-01-15 10:00:00",
    "update_time": "2023-01-15 10:00:00",
    "vip_prices": [
      {
        "vip_level": 0,
        "price": 0.2000,
        "is_current": 0
      },
      {
        "vip_level": 1,
        "price": 0.3000,
        "is_current": 0
      },
      {
        "vip_level": 2,
        "price": 0.5000,
        "is_current": 1
      },
      {
        "vip_level": 3,
        "price": 0.8000,
        "is_current": 0
      }
    ]
  }
}
```

### 9.3 创建用户计划

- **接口路径**: `/user-plans`
- **请求方式**: POST
- **接口描述**: 创建新的用户计划

#### 请求参数

| 参数名             | 类型      | 必填 | 描述                               |
| ------------------ | --------- | ---- | ---------------------------------- |
| plan_id            | string    | 是   | 计划ID                             |
| user_id            | number    | 是   | 用户ID                             |
| click_price        | number    | 是   | 点击单价                           |
| vip_level          | number    | 是   | VIP等级                            |
| min_amount         | number    | 是   | 最低投放金额                       |
| max_amount         | number    | 是   | 最高投放金额                       |
| consumption_minutes| number    | 否   | 消耗时间(分钟)，默认为1            |
| is_official        | number    | 否   | 是否官方推荐，1-是，0-否，默认为0  |
| placement_status   | string    | 否   | 投放状态，默认为"投放中"           |
| vip_prices         | object[]  | 是   | VIP等级价格配置                    |

#### vip_prices参数说明

| 参数名    | 类型   | 必填 | 描述                         |
| --------- | ------ | ---- | ---------------------------- |
| vip_level | number | 是   | VIP等级                      |
| price     | number | 是   | 单价                         |
| is_current| number | 否   | 是否当前使用，1-是，0-否     |

#### 返回结果

```json
{
  "code": 0,
  "message": "创建成功",
  "data": {
    "id": 2,
    "plan_id": "P_23456789",
    "user_id": 1002,
    "click_price": 0.6000,
    "create_time": "2023-06-01 12:00:00"
  }
}
```

### 9.4 更新用户计划

- **接口路径**: `/user-plans/{id}`
- **请求方式**: PUT
- **接口描述**: 更新指定用户计划的信息

#### 请求参数

| 参数名             | 类型      | 必填 | 描述                     |
| ------------------ | --------- | ---- | ------------------------ |
| id                 | number    | 是   | 用户计划ID               |
| click_price        | number    | 否   | 点击单价                 |
| vip_level          | number    | 否   | VIP等级                  |
| min_amount         | number    | 否   | 最低投放金额             |
| max_amount         | number    | 否   | 最高投放金额             |
| consumption_minutes| number    | 否   | 消耗时间(分钟)           |
| is_official        | number    | 否   | 是否官方推荐，1-是，0-否 |
| placement_status   | string    | 否   | 投放状态                 |
| vip_prices         | object[]  | 否   | VIP等级价格配置          |

#### 返回结果

```json
{
  "code": 0,
  "message": "更新成功",
  "data": {
    "id": 1,
    "click_price": 0.6000,
    "update_time": "2023-06-01 13:00:00"
  }
}
```

### 9.5 批量更新用户计划状态

- **接口路径**: `/user-plans/batch-status`
- **请求方式**: PUT
- **接口描述**: 批量更新多个用户计划的状态

#### 请求参数

| 参数名   | 类型     | 必填 | 描述                     |
| -------- | -------- | ---- | ------------------------ |
| ids      | number[] | 是   | 用户计划ID列表           |
| status   | string   | 是   | 新状态(投放中,暂停等)    |

#### 返回结果

```json
{
  "code": 0,
  "message": "批量更新成功",
  "data": {
    "success_count": 3,
    "fail_count": 0,
    "success_ids": [1, 2, 3],
    "fail_ids": []
  }
}
```

### 9.6 用户计划批量结算

- **接口路径**: `/user-plans/batch-settlement`
- **请求方式**: POST
- **接口描述**: 批量结算多个用户计划

#### 请求参数

| 参数名   | 类型     | 必填 | 描述                 |
| -------- | -------- | ---- | -------------------- |
| ids      | number[] | 是   | 用户计划ID列表       |
| remark   | string   | 否   | 备注                 |

#### 返回结果

```json
{
  "code": 0,
  "message": "批量结算成功",
  "data": {
    "success_count": 3,
    "fail_count": 0,
    "success_ids": [1, 2, 3],
    "fail_ids": [],
    "total_amount": 150.00
  }
}
```

### 9.7 导出用户计划

- **接口路径**: `/user-plans/export`
- **请求方式**: GET
- **接口描述**: 导出用户计划数据

#### 请求参数

| 参数名          | 类型     | 必填 | 描述                            |
| --------------- | -------- | ---- | ------------------------------- |
| user_id         | number   | 否   | 用户ID                          |
| plan_id         | number   | 否   | 计划ID                          |
| placement_status| string   | 否   | 投放状态(投放中,暂停等)         |
| vip_level       | number   | 否   | VIP等级                         |
| is_official     | number   | 否   | 是否官方推荐，1-是，0-否        |
| create_time     | string   | 否   | 创建时间，格式：YYYY-MM-DD      |
| ids             | number[] | 否   | 指定ID列表导出                  |
| format          | string   | 否   | 导出格式(csv,excel)，默认为excel|

#### 返回结果

文件流，直接下载

## 10. 数据统计接口

### 10.1 注册统计

#### 10.1.1 获取注册统计概览

- **接口路径**: `/statistics/register/overview`
- **请求方式**: GET
- **接口描述**: 获取用户注册统计概览数据

#### 请求参数

| 参数名     | 类型   | 必填 | 描述                      |
| ---------- | ------ | ---- | ------------------------- |
| start_date | string | 否   | 开始日期，格式：YYYY-MM-DD|
| end_date   | string | 否   | 结束日期，格式：YYYY-MM-DD|

#### 返回结果

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "total_users": 1500,
    "new_users": 120,
    "active_users": 800,
    "conversion_rate": 15.2,
    "retention_rate": 68.5,
    "period_growth": 8.3
  }
}
```

#### 10.1.2 获取注册趋势数据

- **接口路径**: `/statistics/register/trend`
- **请求方式**: GET
- **接口描述**: 获取用户注册趋势数据

#### 请求参数

| 参数名     | 类型   | 必填 | 描述                       |
| ---------- | ------ | ---- | -------------------------- |
| start_date | string | 否   | 开始日期，格式：YYYY-MM-DD |
| end_date   | string | 否   | 结束日期，格式：YYYY-MM-DD |
| granularity| string | 否   | 时间粒度(day,week,month)，默认为day |

#### 返回结果

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "timeline": ["2023-01-01", "2023-01-02", "2023-01-03", "..."],
    "new_users": [25, 30, 18, "..."],
    "active_users": [150, 175, 160, "..."],
    "conversion_rates": [15.2, 16.8, 14.5, "..."]
  }
}
```

#### 10.1.3 获取地域分布数据

- **接口路径**: `/statistics/register/regions`
- **请求方式**: GET
- **接口描述**: 获取用户注册地域分布数据

#### 请求参数

| 参数名     | 类型   | 必填 | 描述                       |
| ---------- | ------ | ---- | -------------------------- |
| start_date | string | 否   | 开始日期，格式：YYYY-MM-DD |
| end_date   | string | 否   | 结束日期，格式：YYYY-MM-DD |

#### 返回结果

```json
{
  "code": 0,
  "message": "success",
  "data": [
    {
      "region": "北美",
      "count": 500,
      "percentage": 33.3
    },
    {
      "region": "欧洲",
      "count": 350,
      "percentage": 23.3
    },
    {
      "region": "亚洲",
      "count": 450,
      "percentage": 30.0
    },
    {
      "region": "其他",
      "count": 200,
      "percentage": 13.4
    }
  ]
}
```

#### 10.1.4 获取渠道来源分析

- **接口路径**: `/statistics/register/sources`
- **请求方式**: GET
- **接口描述**: 获取用户注册渠道来源分析数据

#### 请求参数

| 参数名     | 类型   | 必填 | 描述                       |
| ---------- | ------ | ---- | -------------------------- |
| start_date | string | 否   | 开始日期，格式：YYYY-MM-DD |
| end_date   | string | 否   | 结束日期，格式：YYYY-MM-DD |

#### 返回结果

```json
{
  "code": 0,
  "message": "success",
  "data": [
    {
      "source": "直接访问",
      "count": 300,
      "percentage": 20.0
    },
    {
      "source": "搜索引擎",
      "count": 450,
      "percentage": 30.0
    },
    {
      "source": "社交媒体",
      "count": 350,
      "percentage": 23.3
    },
    {
      "source": "广告投放",
      "count": 250,
      "percentage": 16.7
    },
    {
      "source": "其他",
      "count": 150,
      "percentage": 10.0
    }
  ]
}
```

### 10.2 财务统计

#### 10.2.1 获取财务统计概览

- **接口路径**: `/statistics/finance/overview`
- **请求方式**: GET
- **接口描述**: 获取财务统计概览数据

#### 请求参数

| 参数名     | 类型   | 必填 | 描述                       |
| ---------- | ------ | ---- | -------------------------- |
| start_date | string | 否   | 开始日期，格式：YYYY-MM-DD |
| end_date   | string | 否   | 结束日期，格式：YYYY-MM-DD |

#### 返回结果

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "total_revenue": 125000.00,
    "total_expense": 75000.00,
    "net_profit": 50000.00,
    "profit_margin": 40.0,
    "revenue_growth": 12.5,
    "expense_growth": 8.3,
    "arpu": 83.33,  // 平均每用户收入
    "roi": 1.67     // 投资回报率
  }
}
```

#### 10.2.2 获取收支趋势数据

- **接口路径**: `/statistics/finance/trend`
- **请求方式**: GET
- **接口描述**: 获取财务收支趋势数据

#### 请求参数

| 参数名     | 类型   | 必填 | 描述                                  |
| ---------- | ------ | ---- | ------------------------------------- |
| start_date | string | 否   | 开始日期，格式：YYYY-MM-DD            |
| end_date   | string | 否   | 结束日期，格式：YYYY-MM-DD            |
| granularity| string | 否   | 时间粒度(day,week,month)，默认为month |

#### 返回结果

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "timeline": ["2023-01", "2023-02", "2023-03", "..."],
    "revenue": [38000.00, 42000.00, 45000.00, "..."],
    "expense": [22000.00, 25000.00, 28000.00, "..."],
    "profit": [16000.00, 17000.00, 17000.00, "..."],
    "roi": [1.73, 1.68, 1.61, "..."]
  }
}
```

#### 10.2.3 获取资金流向分析

- **接口路径**: `/statistics/finance/cash-flow`
- **请求方式**: GET
- **接口描述**: 获取资金流向分析数据

#### 请求参数

| 参数名     | 类型   | 必填 | 描述                       |
| ---------- | ------ | ---- | -------------------------- |
| start_date | string | 否   | 开始日期，格式：YYYY-MM-DD |
| end_date   | string | 否   | 结束日期，格式：YYYY-MM-DD |

#### 返回结果

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "income": [
      {
        "type": "充值",
        "amount": 80000.00,
        "percentage": 64.0
      },
      {
        "type": "代运营收入",
        "amount": 30000.00,
        "percentage": 24.0
      },
      {
        "type": "其他收入",
        "amount": 15000.00,
        "percentage": 12.0
      }
    ],
    "expense": [
      {
        "type": "广告投放",
        "amount": 45000.00,
        "percentage": 60.0
      },
      {
        "type": "提现",
        "amount": 20000.00,
        "percentage": 26.7
      },
      {
        "type": "系统运营",
        "amount": 10000.00,
        "percentage": 13.3
      }
    ]
  }
}
```

#### 10.2.4 获取用户消费行为分析

- **接口路径**: `/statistics/finance/user-behavior`
- **请求方式**: GET
- **接口描述**: 获取用户消费行为分析数据

#### 请求参数

| 参数名     | 类型   | 必填 | 描述                       |
| ---------- | ------ | ---- | -------------------------- |
| start_date | string | 否   | 开始日期，格式：YYYY-MM-DD |
| end_date   | string | 否   | 结束日期，格式：YYYY-MM-DD |

#### 返回结果

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "user_segments": [
      {
        "segment": "高消费用户",
        "count": 150,
        "percentage": 10.0,
        "total_spend": 45000.00,
        "avg_spend": 300.00
      },
      {
        "segment": "中消费用户",
        "count": 450,
        "percentage": 30.0,
        "total_spend": 22500.00,
        "avg_spend": 50.00
      },
      {
        "segment": "低消费用户",
        "count": 900,
        "percentage": 60.0,
        "total_spend": 7500.00,
        "avg_spend": 8.33
      }
    ],
    "consumption_distribution": {
      "ranges": ["0-10", "10-50", "50-100", "100-500", "500+"],
      "counts": [600, 300, 300, 225, 75]
    }
  }
} 

## 11. 用户管理接口

### 11.1 获取用户列表

- **接口路径**: `/users`
- **请求方式**: GET
- **接口描述**: 获取用户列表，支持分页和筛选

#### 请求参数

| 参数名       | 类型   | 必填 | 描述                     |
| ------------ | ------ | ---- | ------------------------ |
| page         | number | 否   | 页码，默认1              |
| page_size    | number | 否   | 每页条数，默认20         |
| username     | string | 否   | 用户名，支持模糊搜索      |
| fb_id        | string | 否   | Facebook ID，支持模糊搜索 |
| fb_nickname  | string | 否   | Facebook昵称，支持模糊搜索|
| vip_level    | number | 否   | VIP等级                  |
| credit_code  | string | 否   | 信用代码                 |
| star_level   | number | 否   | 星级                     |
| invite_code  | string | 否   | 邀请码                   |
| agency_id    | string | 否   | 代运营ID                 |
| admin_id     | number | 否   | 所属管理员ID             |
| status       | string | 否   | 用户状态(启用,禁用)      |
| create_time  | string | 否   | 创建时间，格式：YYYY-MM-DD|

#### 返回结果

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "total": 1500,
    "page": 1,
    "page_size": 20,
    "list": [
      {
        "id": 1001,
        "username": "user1001",
        "fb_id": "FB10001",
        "fb_nickname": "User 1001",
        "fb_avatar": "https://example.com/avatar1.png",
        "vip_level": 2,
        "credit_code": "A1",
        "star_level": 3,
        "invite_code": "INV1001",
        "agency_id": "AGY12345678",
        "admin_id": 1,
        "admin_name": "系统管理员",
        "status": "启用",
        "last_login_time": "2023-04-01 10:00:00",
        "create_time": "2023-01-01 00:00:00",
        "wallet": {
          "total_assets": 500.00,
          "available_balance": 250.00
        }
      }
    ]
  }
}
```

### 11.2 获取用户详情

- **接口路径**: `/users/{id}`
- **请求方式**: GET
- **接口描述**: 获取指定用户的详细信息

#### 请求参数

| 参数名 | 类型   | 必填 | 描述   |
| ------ | ------ | ---- | ------ |
| id     | number | 是   | 用户ID |

#### 返回结果

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 1001,
    "username": "user1001",
    "fb_id": "FB10001",
    "auth_code": "AC234567890",
    "fb_nickname": "User 1001",
    "fb_avatar": "https://example.com/avatar1.png",
    "fb_lang": "en_US",
    "fb_time_zone": "GMT+8",
    "vip_level": 2,
    "credit_code": "A1",
    "star_level": 3,
    "invite_code": "INV1001",
    "agency_id": "AGY12345678",
    "admin_id": 1,
    "admin_name": "系统管理员",
    "status": "启用",
    "last_login_time": "2023-04-01 10:00:00",
    "create_time": "2023-01-01 00:00:00",
    "update_time": "2023-04-01 10:00:00",
    "wallet": {
      "id": 1001,
      "total_assets": 500.00,
      "available_balance": 250.00,
      "pending_consumption": 200.00,
      "pending_settlement": 50.00,
      "currency": "USD"
    },
    "payment_methods": [
      {
        "id": 1,
        "payment_code": "PAY_001",
        "type": "bank_card",
        "name": "招商银行",
        "account_name": "张三",
        "masked_no": "6225****8888",
        "status": "active"
      }
    ]
  }
}
```

### 11.3 创建用户

- **接口路径**: `/users`
- **请求方式**: POST
- **接口描述**: 创建新用户

#### 请求参数

| 参数名       | 类型   | 必填 | 描述                     |
| ------------ | ------ | ---- | ------------------------ |
| username     | string | 是   | 用户名                   |
| fb_id        | string | 是   | Facebook ID              |
| auth_code    | string | 否   | 授权码                   |
| fb_nickname  | string | 否   | Facebook昵称             |
| fb_avatar    | string | 否   | Facebook头像URL          |
| fb_lang      | string | 否   | Facebook语言设置         |
| fb_time_zone | string | 否   | Facebook时区             |
| vip_level    | number | 否   | VIP等级，默认为0         |
| credit_code  | string | 否   | 信用代码                 |
| star_level   | number | 否   | 星级，默认为0            |
| invite_code  | string | 否   | 邀请码                   |
| agency_id    | string | 否   | 代运营ID                 |
| admin_id     | number | 否   | 所属管理员ID             |
| status       | string | 否   | 用户状态，默认为"启用"   |

#### 返回结果

```json
{
  "code": 0,
  "message": "创建成功",
  "data": {
    "id": 1002,
    "username": "user1002",
    "fb_id": "FB10002",
    "invite_code": "INV1002",
    "create_time": "2023-06-01 12:00:00"
  }
}
```

### 11.4 更新用户信息

- **接口路径**: `/users/{id}`
- **请求方式**: PUT
- **接口描述**: 更新指定用户的信息

#### 请求参数

| 参数名       | 类型   | 必填 | 描述                 |
| ------------ | ------ | ---- | -------------------- |
| id           | number | 是   | 用户ID               |
| fb_nickname  | string | 否   | Facebook昵称         |
| fb_avatar    | string | 否   | Facebook头像URL      |
| vip_level    | number | 否   | VIP等级              |
| credit_code  | string | 否   | 信用代码             |
| star_level   | number | 否   | 星级                 |
| agency_id    | string | 否   | 代运营ID             |
| admin_id     | number | 否   | 所属管理员ID         |
| status       | string | 否   | 用户状态(启用,禁用)  |

#### 返回结果

```json
{
  "code": 0,
  "message": "更新成功",
  "data": {
    "id": 1001,
    "vip_level": 3,
    "update_time": "2023-06-01 13:00:00"
  }
}
```

### 11.5 Facebook ID 换绑

- **接口路径**: `/users/{id}/fb-id`
- **请求方式**: PUT
- **接口描述**: 更换用户的Facebook ID

#### 请求参数

| 参数名       | 类型   | 必填 | 描述           |
| ------------ | ------ | ---- | -------------- |
| id           | number | 是   | 用户ID         |
| new_fb_id    | string | 是   | 新Facebook ID  |
| reason       | string | 是   | 换绑原因       |

#### 返回结果

```json
{
  "code": 0,
  "message": "Facebook ID换绑成功",
  "data": {
    "id": 1001,
    "old_fb_id": "FB10001",
    "new_fb_id": "FB20001",
    "update_time": "2023-06-01 13:00:00"
  }
}
```

### 11.6 批量操作接口

#### 11.6.1 批量充值

- **接口路径**: `/users/batch-recharge`
- **请求方式**: POST
- **接口描述**: 为多个用户批量充值

#### 请求参数

| 参数名   | 类型     | 必填 | 描述         |
| -------- | -------- | ---- | ------------ |
| user_ids | number[] | 是   | 用户ID列表   |
| amount   | number   | 是   | 充值金额     |
| type     | string   | 是   | 充值类型     |
| remark   | string   | 否   | 备注         |

#### 返回结果

```json
{
  "code": 0,
  "message": "批量充值成功",
  "data": {
    "success_count": 3,
    "fail_count": 0,
    "success_users": [1001, 1002, 1003],
    "fail_users": [],
    "total_amount": 300.00
  }
}
```

#### 11.6.2 批量扣款

- **接口路径**: `/users/batch-deduct`
- **请求方式**: POST
- **接口描述**: 从多个用户账户批量扣款

#### 请求参数

| 参数名   | 类型     | 必填 | 描述         |
| -------- | -------- | ---- | ------------ |
| user_ids | number[] | 是   | 用户ID列表   |
| amount   | number   | 是   | 扣款金额     |
| remark   | string   | 否   | 备注         |

#### 返回结果

```json
{
  "code": 0,
  "message": "批量扣款成功",
  "data": {
    "success_count": 3,
    "fail_count": 0,
    "success_users": [1001, 1002, 1003],
    "fail_users": [],
    "total_amount": 150.00
  }
}
```

#### 11.6.3 批量派发优惠券

- **接口路径**: `/users/batch-coupon`
- **请求方式**: POST
- **接口描述**: 为多个用户批量派发优惠券

#### 请求参数

| 参数名    | 类型     | 必填 | 描述         |
| --------- | -------- | ---- | ------------ |
| user_ids  | number[] | 是   | 用户ID列表   |
| coupon_id | number   | 是   | 优惠券ID     |
| remark    | string   | 否   | 备注         |

#### 返回结果

```json
{
  "code": 0,
  "message": "批量派发优惠券成功",
  "data": {
    "success_count": 3,
    "fail_count": 0,
    "success_users": [1001, 1002, 1003],
    "fail_users": []
  }
}
```

#### 11.6.4 批量派单

- **接口路径**: `/users/batch-plan`
- **请求方式**: POST
- **接口描述**: 为多个用户批量派单(分配计划)

#### 请求参数

| 参数名    | 类型     | 必填 | 描述                             |
| --------- | -------- | ---- | -------------------------------- |
| user_ids  | number[] | 是   | 用户ID列表                       |
| plan_id   | string   | 是   | 计划ID                           |
| mode      | string   | 是   | 派单模式(manual,auto)            |
| settings  | object   | 否   | 派单设置(mode为auto时必填)       |

#### settings参数说明

| 参数名             | 类型  | 必填 | 描述                 |
| ------------------ | ----- | ---- | -------------------- |
| click_price        | number| 是   | 点击单价             |
| vip_level          | number| 是   | VIP等级              |
| min_amount         | number| 是   | 最低投放金额         |
| max_amount         | number| 是   | 最高投放金额         |
| consumption_minutes| number| 否   | 消耗时间(分钟)       |
| is_official        | number| 否   | 是否官方推荐         |

#### 返回结果

```json
{
  "code": 0,
  "message": "批量派单成功",
  "data": {
    "success_count": 3,
    "fail_count": 0,
    "success_users": [1001, 1002, 1003],
    "fail_users": [],
    "user_plans": [
      {"id": 3, "user_id": 1001, "plan_id": "P_12345678"},
      {"id": 4, "user_id": 1002, "plan_id": "P_12345678"},
      {"id": 5, "user_id": 1003, "plan_id": "P_12345678"}
    ]
  }
}
```

#### 11.6.5 用户批量转移

- **接口路径**: `/users/batch-transfer`
- **请求方式**: POST
- **接口描述**: 将多个用户批量转移给新的管理员

#### 请求参数

| 参数名     | 类型     | 必填 | 描述           |
| ---------- | -------- | ---- | -------------- |
| user_ids   | number[] | 是   | 用户ID列表     |
| admin_id   | number   | 是   | 目标管理员ID   |
| remark     | string   | 否   | 备注           |

#### 返回结果

```json
{
  "code": 0,
  "message": "批量转移成功",
  "data": {
    "success_count": 3,
    "fail_count": 0,
    "success_users": [1001, 1002, 1003],
    "fail_users": []
  }
}
```

## 12. 用户反馈接口

### 12.1 用户反馈列表

#### 12.1.1 获取反馈列表

- **接口路径**: `/feedbacks`
- **请求方式**: GET
- **接口描述**: 获取用户反馈列表，支持分页和筛选

#### 请求参数

| 参数名      | 类型   | 必填 | 描述                                      |
| ----------- | ------ | ---- | ----------------------------------------- |
| page        | number | 否   | 页码，默认1                               |
| page_size   | number | 否   | 每页条数，默认20                          |
| user_id     | number | 否   | 用户ID                                    |
| title       | string | 否   | 反馈标题，支持模糊搜索                    |
| status      | string | 否   | 状态(pending,processing,completed)       |
| start_time  | string | 否   | 创建时间范围起点，格式：YYYY-MM-DD       |
| end_time    | string | 否   | 创建时间范围终点，格式：YYYY-MM-DD       |

#### 返回结果

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "total": 100,
    "page": 1,
    "page_size": 20,
    "list": [
      {
        "id": 1,
        "feedback_code": "FB001",
        "user_id": 1001,
        "user_name": "用户1001",
        "title": "系统使用问题",
        "content": "在使用系统过程中遇到了一些问题...",
        "status": "pending",
        "reply": null,
        "reply_time": null,
        "create_time": "2023-05-01 10:00:00"
      }
    ]
  }
}
```

#### 12.1.2 获取反馈详情

- **接口路径**: `/feedbacks/{id}`
- **请求方式**: GET
- **接口描述**: 获取指定反馈的详细信息

#### 请求参数

| 参数名 | 类型   | 必填 | 描述     |
| ------ | ------ | ---- | -------- |
| id     | number | 是   | 反馈ID   |

#### 返回结果

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 1,
    "feedback_code": "FB001",
    "user_id": 1001,
    "user_name": "用户1001",
    "title": "系统使用问题",
    "content": "在使用系统过程中遇到了一些问题...",
    "status": "pending",
    "reply": null,
    "reply_time": null,
    "create_time": "2023-05-01 10:00:00",
    "update_time": "2023-05-01 10:00:00"
  }
}
```

#### 12.1.3 回复反馈

- **接口路径**: `/feedbacks/{id}/reply`
- **请求方式**: POST
- **接口描述**: 回复指定用户反馈

#### 请求参数

| 参数名 | 类型   | 必填 | 描述                                 |
| ------ | ------ | ---- | ------------------------------------ |
| id     | number | 是   | 反馈ID                               |
| reply  | string | 是   | 回复内容                             |
| status | string | 否   | 更新状态，默认为"completed"          |

#### 返回结果

```json
{
  "code": 0,
  "message": "回复成功",
  "data": {
    "id": 1,
    "status": "completed",
    "reply": "您好，针对您反馈的问题...",
    "reply_time": "2023-06-01 13:00:00"
  }
}
```

#### 12.1.4 更新反馈状态

- **接口路径**: `/feedbacks/{id}/status`
- **请求方式**: PUT
- **接口描述**: 更新指定反馈的状态

#### 请求参数

| 参数名 | 类型   | 必填 | 描述                                  |
| ------ | ------ | ---- | ------------------------------------- |
| id     | number | 是   | 反馈ID                                |
| status | string | 是   | 新状态(pending,processing,completed) |

#### 返回结果

```json
{
  "code": 0,
  "message": "状态更新成功",
  "data": {
    "id": 1,
    "status": "processing",
    "update_time": "2023-06-01 13:00:00"
  }
}
```

### 12.2 通知管理

#### 12.2.1 获取通知列表

- **接口路径**: `/notifications`
- **请求方式**: GET
- **接口描述**: 获取系统通知列表，支持分页和筛选

#### 请求参数

| 参数名      | 类型   | 必填 | 描述                                         |
| ----------- | ------ | ---- | -------------------------------------------- |
| page        | number | 否   | 页码，默认1                                  |
| page_size   | number | 否   | 每页条数，默认20                             |
| user_id     | number | 否   | 用户ID                                       |
| title       | string | 否   | 通知标题，支持模糊搜索                       |
| type        | string | 否   | 类型(system,maintenance,activity)           |
| status      | string | 否   | 状态(pending,processing,completed)          |
| is_read     | number | 否   | 是否已读，1-是，0-否                         |
| start_time  | string | 否   | 创建时间范围起点，格式：YYYY-MM-DD          |
| end_time    | string | 否   | 创建时间范围终点，格式：YYYY-MM-DD          |

#### 返回结果

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "total": 100,
    "page": 1,
    "page_size": 20,
    "list": [
      {
        "id": 1,
        "notice_code": "NOTE001",
        "user_id": 1001,
        "user_name": "用户1001",
        "title": "系统维护通知",
        "content": "系统将于2023年6月1日进行维护升级...",
        "type": "maintenance",
        "status": "pending",
        "is_read": 0,
        "link": null,
        "expire_time": "2023-06-02 00:00:00",
        "create_time": "2023-05-30 10:00:00"
      }
    ]
  }
}
```

#### 12.2.2 获取通知详情

- **接口路径**: `/notifications/{id}`
- **请求方式**: GET
- **接口描述**: 获取指定通知的详细信息

#### 请求参数

| 参数名 | 类型   | 必填 | 描述     |
| ------ | ------ | ---- | -------- |
| id     | number | 是   | 通知ID   |

#### 返回结果

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 1,
    "notice_code": "NOTE001",
    "user_id": 1001,
    "user_name": "用户1001",
    "title": "系统维护通知",
    "content": "系统将于2023年6月1日进行维护升级...",
    "type": "maintenance",
    "status": "pending",
    "is_read": 0,
    "link": null,
    "expire_time": "2023-06-02 00:00:00",
    "create_time": "2023-05-30 10:00:00",
    "update_time": "2023-05-30 10:00:00"
  }
}
```

#### 12.2.3 创建通知

- **接口路径**: `/notifications`
- **请求方式**: POST
- **接口描述**: 创建新的系统通知

#### 请求参数

| 参数名      | 类型     | 必填 | 描述                              |
| ----------- | -------- | ---- | --------------------------------- |
| user_ids    | number[] | 否   | 用户ID列表（为空表示发送给所有用户）|
| title       | string   | 是   | 通知标题                          |
| content     | string   | 是   | 通知内容                          |
| type        | string   | 是   | 类型(system,maintenance,activity) |
| link        | string   | 否   | 相关链接                          |
| expire_time | string   | 否   | 过期时间，格式：YYYY-MM-DD HH:MM:SS|

#### 返回结果

```json
{
  "code": 0,
  "message": "创建成功",
  "data": {
    "id": 2,
    "notice_code": "NOTE002",
    "title": "活动通知",
    "type": "activity",
    "target_user_count": 5,
    "create_time": "2023-06-01 12:00:00"
  }
}
```

#### 12.2.4 更新通知

- **接口路径**: `/notifications/{id}`
- **请求方式**: PUT
- **接口描述**: 更新指定通知的信息

#### 请求参数

| 参数名      | 类型   | 必填 | 描述                              |
| ----------- | ------ | ---- | --------------------------------- |
| id          | number | 是   | 通知ID                            |
| title       | string | 否   | 通知标题                          |
| content     | string | 否   | 通知内容                          |
| status      | string | 否   | 状态(pending,processing,completed)|
| link        | string | 否   | 相关链接                          |
| expire_time | string | 否   | 过期时间，格式：YYYY-MM-DD HH:MM:SS|

#### 返回结果

```json
{
  "code": 0,
  "message": "更新成功",
  "data": {
    "id": 1,
    "title": "系统紧急维护通知",
    "update_time": "2023-06-01 13:00:00"
  }
}
```

#### 12.2.5 删除通知

- **接口路径**: `/notifications/{id}`
- **请求方式**: DELETE
- **接口描述**: 删除指定通知

#### 请求参数

| 参数名 | 类型   | 必填 | 描述     |
| ------ | ------ | ---- | -------- |
| id     | number | 是   | 通知ID   |

#### 返回结果

```json
{
  "code": 0,
  "message": "删除成功",
  "data": null
}
```

## 13. 系统设置接口

### 13.1 获取系统参数列表

- **接口路径**: `/settings/params`
- **请求方式**: GET
- **接口描述**: 获取系统参数列表，支持分页和筛选

#### 请求参数

| 参数名    | 类型   | 必填 | 描述             |
| --------- | ------ | ---- | ---------------- |
| page      | number | 否   | 页码，默认1      |
| page_size | number | 否   | 每页条数，默认20 |
| key       | string | 否   | 参数键名         |
| group     | string | 否   | 参数分组         |

#### 返回结果

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "total": 50,
    "page": 1,
    "page_size": 20,
    "list": [
      {
        "id": 1,
        "key": "site_name",
        "value": "ADS Admin System",
        "type": "string",
        "group": "basic",
        "description": "网站名称",
        "create_time": "2023-01-01 00:00:00",
        "update_time": "2023-01-01 00:00:00"
      },
      {
        "id": 2,
        "key": "site_logo",
        "value": "https://example.com/logo.png",
        "type": "string",
        "group": "basic",
        "description": "网站Logo",
        "create_time": "2023-01-01 00:00:00",
        "update_time": "2023-01-01 00:00:00"
      }
    ]
  }
}
```

### 13.2 获取系统参数详情

- **接口路径**: `/settings/params/{id}`
- **请求方式**: GET
- **接口描述**: 获取指定系统参数的详细信息

#### 请求参数

| 参数名 | 类型   | 必填 | 描述         |
| ------ | ------ | ---- | ------------ |
| id     | number | 是   | 系统参数ID   |

#### 返回结果

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 1,
    "key": "site_name",
    "value": "ADS Admin System",
    "type": "string",
    "group": "basic",
    "description": "网站名称",
    "create_time": "2023-01-01 00:00:00",
    "update_time": "2023-01-01 00:00:00"
  }
}
```

### 13.3 更新系统参数

- **接口路径**: `/settings/params/{id}`
- **请求方式**: PUT
- **接口描述**: 更新指定系统参数的值

#### 请求参数

| 参数名 | 类型   | 必填 | 描述       |
| ------ | ------ | ---- | ---------- |
| id     | number | 是   | 系统参数ID |
| value  | string | 是   | 参数值     |

#### 返回结果

```json
{
  "code": 0,
  "message": "更新成功",
  "data": {
    "id": 1,
    "key": "site_name",
    "value": "ADS Admin System V2",
    "update_time": "2023-06-01 13:00:00"
  }
}
```

### 13.4 批量更新系统参数

- **接口路径**: `/settings/params/batch`
- **请求方式**: PUT
- **接口描述**: 批量更新多个系统参数的值

#### 请求参数

| 参数名  | 类型     | 必填 | 描述                        |
| ------- | -------- | ---- | --------------------------- |
| params  | object[] | 是   | 参数列表                    |

#### params参数说明

| 参数名 | 类型   | 必填 | 描述       |
| ------ | ------ | ---- | ---------- |
| id     | number | 是   | 系统参数ID |
| value  | string | 是   | 参数值     |

#### 返回结果

```json
{
  "code": 0,
  "message": "批量更新成功",
  "data": {
    "success_count": 2,
    "fail_count": 0,
    "success_ids": [1, 2],
    "fail_ids": []
  }
}
```

### 13.5 获取系统参数分组

- **接口路径**: `/settings/param-groups`
- **请求方式**: GET
- **接口描述**: 获取系统参数分组列表

#### 请求参数

无

#### 返回结果

```json
{
  "code": 0,
  "message": "success",
  "data": [
    {
      "group": "basic",
      "name": "基础设置",
      "sort_order": 0
    },
    {
      "group": "payment",
      "name": "支付设置",
      "sort_order": 1
    },
    {
      "group": "email",
      "name": "邮件设置",
      "sort_order": 2
    },
    {
      "group": "security",
      "name": "安全设置",
      "sort_order": 3
    }
  ]
}
```

### 13.6 获取系统日志

- **接口路径**: `/settings/logs`
- **请求方式**: GET
- **接口描述**: 获取系统操作日志，支持分页和筛选

#### 请求参数

| 参数名     | 类型   | 必填 | 描述                       |
| ---------- | ------ | ---- | -------------------------- |
| page       | number | 否   | 页码，默认1                |
| page_size  | number | 否   | 每页条数，默认20           |
| admin_id   | number | 否   | 管理员ID                   |
| module     | string | 否   | 模块名称                   |
| action     | string | 否   | 操作类型                   |
| ip         | string | 否   | 操作IP                     |
| start_time | string | 否   | 开始时间，格式：YYYY-MM-DD |
| end_time   | string | 否   | 结束时间，格式：YYYY-MM-DD |

#### 返回结果

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "total": 200,
    "page": 1,
    "page_size": 20,
    "list": [
      {
        "id": 1,
        "admin_id": 1,
        "admin_name": "系统管理员",
        "module": "系统设置",
        "action": "更新参数",
        "description": "更新系统参数: site_name",
        "ip": "***********",
        "user_agent": "Mozilla/5.0...",
        "request_url": "/api/settings/params/1",
        "request_method": "PUT",
        "create_time": "2023-06-01 13:00:00"
      }
    ]
  }
}
```

## 14. 国家管理接口

### 14.1 获取国家列表

- **接口路径**: `/countries`
- **请求方式**: GET
- **接口描述**: 获取国家/地区列表，支持分页和筛选

#### 请求参数

| 参数名    | 类型   | 必填 | 描述                  |
| --------- | ------ | ---- | --------------------- |
| page      | number | 否   | 页码，默认1           |
| page_size | number | 否   | 每页条数，默认20      |
| name      | string | 否   | 国家/地区名称，支持模糊搜索 |
| code      | string | 否   | 国家/地区代码         |
| region    | string | 否   | 所属区域              |
| status    | string | 否   | 状态(启用,禁用)       |

#### 返回结果

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "total": 200,
    "page": 1,
    "page_size": 20,
    "list": [
      {
        "id": 1,
        "name": "中国",
        "english_name": "China",
        "code": "CN",
        "calling_code": "86",
        "region": "亚洲",
        "flag_icon": "https://example.com/flags/cn.png",
        "status": "启用",
        "sort_order": 0,
        "create_time": "2023-01-01 00:00:00"
      },
      {
        "id": 2,
        "name": "美国",
        "english_name": "United States",
        "code": "US",
        "calling_code": "1",
        "region": "北美洲",
        "flag_icon": "https://example.com/flags/us.png",
        "status": "启用",
        "sort_order": 1,
        "create_time": "2023-01-01 00:00:00"
      }
    ]
  }
}
```

### 14.2 获取国家详情

- **接口路径**: `/countries/{id}`
- **请求方式**: GET
- **接口描述**: 获取指定国家/地区的详细信息

#### 请求参数

| 参数名 | 类型   | 必填 | 描述           |
| ------ | ------ | ---- | -------------- |
| id     | number | 是   | 国家/地区ID    |

#### 返回结果

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 1,
    "name": "中国",
    "english_name": "China",
    "code": "CN",
    "calling_code": "86",
    "region": "亚洲",
    "flag_icon": "https://example.com/flags/cn.png",
    "currency_code": "CNY",
    "currency_symbol": "¥",
    "timezone": "UTC+8",
    "status": "启用",
    "sort_order": 0,
    "rules": {
      "restricted_content": ["政治敏感内容"],
      "special_requirements": ["需要ICP备案"]
    },
    "create_time": "2023-01-01 00:00:00",
    "update_time": "2023-01-01 00:00:00"
  }
}
```

### 14.3 创建国家/地区

- **接口路径**: `/countries`
- **请求方式**: POST
- **接口描述**: 创建新的国家/地区信息

#### 请求参数

| 参数名         | 类型   | 必填 | 描述                   |
| -------------- | ------ | ---- | ---------------------- |
| name           | string | 是   | 国家/地区名称          |
| english_name   | string | 是   | 英文名称               |
| code           | string | 是   | 国家/地区代码          |
| calling_code   | string | 是   | 国际电话区号           |
| region         | string | 是   | 所属区域               |
| flag_icon      | string | 是   | 国旗图标URL            |
| currency_code  | string | 否   | 货币代码               |
| currency_symbol| string | 否   | 货币符号               |
| timezone       | string | 否   | 时区                   |
| status         | string | 否   | 状态，默认为"启用"     |
| sort_order     | number | 否   | 排序顺序，默认为0      |
| rules          | object | 否   | 特殊规则设置           |

#### 返回结果

```json
{
  "code": 0,
  "message": "创建成功",
  "data": {
    "id": 3,
    "name": "日本",
    "english_name": "Japan",
    "code": "JP",
    "create_time": "2023-06-01 12:00:00"
  }
}
```

### 14.4 更新国家/地区

- **接口路径**: `/countries/{id}`
- **请求方式**: PUT
- **接口描述**: 更新指定国家/地区的信息

#### 请求参数

| 参数名         | 类型   | 必填 | 描述               |
| -------------- | ------ | ---- | ------------------ |
| id             | number | 是   | 国家/地区ID        |
| name           | string | 否   | 国家/地区名称      |
| english_name   | string | 否   | 英文名称           |
| calling_code   | string | 否   | 国际电话区号       |
| region         | string | 否   | 所属区域           |
| flag_icon      | string | 否   | 国旗图标URL        |
| currency_code  | string | 否   | 货币代码           |
| currency_symbol| string | 否   | 货币符号           |
| timezone       | string | 否   | 时区               |
| status         | string | 否   | 状态               |
| sort_order     | number | 否   | 排序顺序           |
| rules          | object | 否   | 特殊规则设置       |

#### 返回结果

```json
{
  "code": 0,
  "message": "更新成功",
  "data": {
    "id": 1,
    "name": "中国",
    "status": "禁用",
    "update_time": "2023-06-01 13:00:00"
  }
}
```

### 14.5 删除国家/地区

- **接口路径**: `/countries/{id}`
- **请求方式**: DELETE
- **接口描述**: 删除指定国家/地区

#### 请求参数

| 参数名 | 类型   | 必填 | 描述        |
| ------ | ------ | ---- | ----------- |
| id     | number | 是   | 国家/地区ID |

#### 返回结果

```json
{
  "code": 0,
  "message": "删除成功",
  "data": null
}
```

### 14.6 获取区域列表

- **接口路径**: `/countries/regions`
- **请求方式**: GET
- **接口描述**: 获取所有区域列表

#### 请求参数

无

#### 返回结果

```json
{
  "code": 0,
  "message": "success",
  "data": [
    {
      "region": "亚洲",
      "english_name": "Asia",
      "count": 48
    },
    {
      "region": "欧洲",
      "english_name": "Europe",
      "count": 50
    },
    {
      "region": "北美洲",
      "english_name": "North America",
      "count": 23
    },
    {
      "region": "南美洲",
      "english_name": "South America",
      "count": 12
    },
    {
      "region": "非洲",
      "english_name": "Africa",
      "count": 54
    },
    {
      "region": "大洋洲",
      "english_name": "Oceania",
      "count": 14
    }
  ]
}
```